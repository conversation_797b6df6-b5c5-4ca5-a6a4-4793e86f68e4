<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.ChatListPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:PetSitterConnect.ViewModels"
             xmlns:services="clr-namespace:PetSitterConnect.Services"
             x:DataType="viewmodels:ChatListViewModel"
             Title="{Binding Title}">

    <Grid RowDefinitions="Auto,*">
        
        <!-- User Role Header -->
        <Border Grid.Row="0"
                Background="{StaticResource PetSoftGradient}"
                StrokeThickness="0"
                Padding="20,15"
                StrokeShape="RoundRectangle 0,0,20,20">
            <Grid ColumnDefinitions="Auto,*,Auto">
                <Label Grid.Column="0"
                       Text="💬"
                       FontSize="24"
                       VerticalOptions="Center" />
                <StackLayout Grid.Column="1"
                             Orientation="Vertical"
                             Spacing="3"
                             Margin="15,0,0,0">
                    <Label Text="{Binding CurrentUser.FullName}"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />
                    <Label Text="💕 Your pet care conversations"
                           FontSize="13"
                           TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
                </StackLayout>
                <Border Grid.Column="2"
                        BackgroundColor="White"
                        StrokeThickness="0"
                        Padding="12,6"
                        StrokeShape="RoundRectangle 15"
                        Shadow="{StaticResource PetSoftGradient}">
                    <Label Text="{Binding UserRoleLabel}"
                           FontSize="11"
                           FontAttributes="Bold"
                           TextColor="{AppThemeBinding Light={StaticResource Secondary}, Dark={StaticResource SecondaryDark}}" />
                </Border>
            </Grid>
        </Border>

        <!-- Conversations List -->
        <RefreshView Grid.Row="1"
                     IsRefreshing="{Binding IsRefreshing}"
                     Command="{Binding RefreshConversationsCommand}">
            
            <CollectionView ItemsSource="{Binding Conversations}"
                            SelectionMode="None">
                
                <CollectionView.EmptyView>
                    <StackLayout HorizontalOptions="Center" VerticalOptions="Center" Spacing="15" Padding="40">
                        <Label Text="💬" FontSize="48" HorizontalOptions="Center" />
                        <Label Text="No conversations yet"
                               FontSize="18"
                               FontAttributes="Bold"
                               HorizontalOptions="Center" />
                        <Label Text="Start chatting when you have active bookings"
                               FontSize="14"
                               TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center" />
                    </StackLayout>
                </CollectionView.EmptyView>

                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="services:ChatConversation">
                        <Grid Padding="15,10" RowDefinitions="Auto">
                            
                            <Border Grid.Row="0"
                                    BackgroundColor="{AppThemeBinding Light=White, Dark={StaticResource Gray900}}"
                                    Stroke="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray700}}"
                                    StrokeThickness="1"
                                    Padding="15"
                                    StrokeShape="RoundRectangle 12"
                                    Margin="0,5">
                                
                                <Grid ColumnDefinitions="Auto,*,Auto" RowDefinitions="Auto,Auto,Auto">
                                    
                                    <!-- Avatar/Icon -->
                                    <Border Grid.Column="0" Grid.RowSpan="3"
                                            BackgroundColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                                            StrokeThickness="0"
                                            WidthRequest="50"
                                            HeightRequest="50"
                                            StrokeShape="RoundRectangle 25"
                                            VerticalOptions="Center"
                                            Margin="0,0,15,0">
                                        <Label Text="👤"
                                               FontSize="24"
                                               TextColor="White"
                                               HorizontalOptions="Center"
                                               VerticalOptions="Center" />
                                    </Border>
                                    
                                    <!-- Conversation Title -->
                                    <Label Grid.Column="1" Grid.Row="0"
                                           Text="{Binding OtherParticipantName}"
                                           FontSize="16"
                                           FontAttributes="Bold"
                                           TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark=White}" />
                                    
                                    <!-- Pet Name -->
                                    <Label Grid.Column="1" Grid.Row="1"
                                           Text="{Binding Booking.PetCareRequest.Pet.Name, StringFormat='Care for {0}'}"
                                           FontSize="14"
                                           TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                                           Margin="0,2,0,0" />
                                    
                                    <!-- Last Message Preview -->
                                    <Label Grid.Column="1" Grid.Row="2"
                                           Text="{Binding LastMessage.Content}"
                                           FontSize="13"
                                           TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}"
                                           MaxLines="2"
                                           LineBreakMode="TailTruncation"
                                           Margin="0,5,0,0" />
                                    
                                    <!-- Time and Unread Badge -->
                                    <StackLayout Grid.Column="2" Grid.RowSpan="3" 
                                                 VerticalOptions="Center" 
                                                 Spacing="5">
                                        
                                        <!-- Time -->
                                        <Label Text="{Binding LastActivity, StringFormat='{0:HH:mm}'}"
                                               FontSize="12"
                                               TextColor="{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray400}}"
                                               HorizontalOptions="End" />
                                        
                                        <!-- Unread Count Badge -->
                                        <Border BackgroundColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                                                StrokeThickness="0"
                                                Padding="6,3"
                                                StrokeShape="RoundRectangle 10"
                                                HorizontalOptions="End"
                                                IsVisible="{Binding UnreadCount, Converter={StaticResource CountToBoolConverter}}">
                                            <Label Text="{Binding UnreadCount}"
                                                   FontSize="11"
                                                   FontAttributes="Bold"
                                                   TextColor="White" />
                                        </Border>
                                    </StackLayout>
                                </Grid>
                            </Border>
                            
                            <!-- Tap Gesture -->
                            <Grid.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:ChatListViewModel}}, Path=OpenChatCommand}"
                                                      CommandParameter="{Binding .}" />
                            </Grid.GestureRecognizers>
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </RefreshView>

        <!-- Loading Indicator -->
        <ActivityIndicator Grid.Row="1"
                           IsVisible="{Binding IsBusy}"
                           IsRunning="{Binding IsBusy}"
                           Color="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}"
                           HorizontalOptions="Center"
                           VerticalOptions="Center" />
    </Grid>

</ContentPage>
