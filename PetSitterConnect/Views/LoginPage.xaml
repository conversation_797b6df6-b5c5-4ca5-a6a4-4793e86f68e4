<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.LoginPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:PetSitterConnect.ViewModels"
             x:DataType="viewmodels:LoginViewModel"
             Title="{Binding Title}"
             Shell.NavBarIsVisible="False">

    <ScrollView>
        <Grid RowDefinitions="Auto,*,Auto" Style="{StaticResource PetWarmGradientBackground}">

            <!-- Header -->
            <Border Grid.Row="0" Style="{StaticResource PetCardFrame}" Margin="20,40,20,20">
                <StackLayout Spacing="15">
                    <Image Source="pet_logo.svg"
                           HeightRequest="120"
                           WidthRequest="120"
                           HorizontalOptions="Center" />
                    <Label Text="🐾 PetSitter Connect"
                           Style="{StaticResource PetHeaderLabel}" />
                    <Label Text="Welcome back! Connect with loving pet care."
                           Style="{StaticResource PetSubHeaderLabel}" />
                </StackLayout>
            </Border>

            <!-- Login Form -->
            <Border Grid.Row="1" Style="{StaticResource PetCardFrame}" Margin="20">
                <StackLayout Spacing="25">

                    <!-- Email Entry -->
                    <StackLayout Spacing="8">
                        <Label Text="📧 Email"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />
                        <Border BackgroundColor="{AppThemeBinding Light={StaticResource PetSurface}, Dark={StaticResource PetSurfaceDark}}"
                                Stroke="{AppThemeBinding Light={StaticResource PetAccent2}, Dark={StaticResource Gray600}}"
                                StrokeThickness="2"
                                StrokeShape="RoundRectangle 12"
                                Padding="15,10">
                            <Entry Text="{Binding Email}"
                                   Placeholder="Enter your email"
                                   Keyboard="Email"
                                   ReturnType="Next"
                                   BackgroundColor="Transparent" />
                        </Border>
                    </StackLayout>

                    <!-- Password Entry -->
                    <StackLayout Spacing="8">
                        <Label Text="🔒 Password"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />
                        <Border BackgroundColor="{AppThemeBinding Light={StaticResource PetSurface}, Dark={StaticResource PetSurfaceDark}}"
                                Stroke="{AppThemeBinding Light={StaticResource PetAccent2}, Dark={StaticResource Gray600}}"
                                StrokeThickness="2"
                                StrokeShape="RoundRectangle 12"
                                Padding="15,10">
                            <Entry Text="{Binding Password}"
                                   Placeholder="Enter your password"
                                   IsPassword="True"
                                   ReturnType="Done"
                                   BackgroundColor="Transparent" />
                        </Border>
                    </StackLayout>

                    <!-- Remember Me -->
                    <StackLayout Orientation="Horizontal" Spacing="10">
                        <CheckBox IsChecked="{Binding RememberMe}"
                                  Color="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />
                        <Label Text="Remember me"
                               VerticalOptions="Center"
                               TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
                    </StackLayout>

                    <!-- Error Message -->
                    <Border BackgroundColor="#FFEBEE"
                            Stroke="#F44336"
                            StrokeThickness="1"
                            StrokeShape="RoundRectangle 8"
                            Padding="15,10"
                            IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}">
                        <Label Text="{Binding ErrorMessage}"
                               TextColor="#D32F2F"
                               FontSize="14" />
                    </Border>

                    <!-- Login Button -->
                    <Button Text="🐾 Sign In"
                            Command="{Binding LoginCommand}"
                            Style="{StaticResource PetAccentButton}"
                            FontSize="18"
                            HeightRequest="55"
                            IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />

                    <!-- Activity Indicator -->
                    <ActivityIndicator IsVisible="{Binding IsBusy}"
                                       IsRunning="{Binding IsBusy}" />

                    <!-- Forgot Password -->
                    <Button Text="Forgot Password?"
                            Command="{Binding ForgotPasswordCommand}"
                            BackgroundColor="Transparent"
                            TextColor="{AppThemeBinding Light={StaticResource Tertiary}, Dark={StaticResource TertiaryDark}}"
                            FontSize="14"
                            FontAttributes="Bold" />

                </StackLayout>
            </Border>

            <!-- Footer -->
            <Border Grid.Row="2" Style="{StaticResource PetCardFrame}" Margin="20,0,20,20">
                <StackLayout Spacing="15">
                    <BoxView HeightRequest="2"
                             BackgroundColor="{AppThemeBinding Light={StaticResource PetAccent2}, Dark={StaticResource Gray600}}"
                             CornerRadius="1" />

                    <StackLayout Orientation="Horizontal" HorizontalOptions="Center" Spacing="8">
                        <Label Text="New to pet care? 🐕"
                               FontSize="14"
                               TextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />
                        <Button Text="Join Us!"
                                Command="{Binding NavigateToRegisterCommand}"
                                BackgroundColor="Transparent"
                                TextColor="{AppThemeBinding Light={StaticResource Secondary}, Dark={StaticResource SecondaryDark}}"
                                FontSize="14"
                                FontAttributes="Bold"
                                Padding="8,4"
                                CornerRadius="8" />
                    </StackLayout>
                </StackLayout>
            </Border>

        </Grid>
    </ScrollView>

</ContentPage>
