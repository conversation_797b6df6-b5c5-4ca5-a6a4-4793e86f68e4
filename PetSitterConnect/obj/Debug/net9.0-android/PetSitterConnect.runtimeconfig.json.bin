2MVVMTOOLKIT_ENABLE_INOTIFYPROPERTYCHANGING_SUPPORTtrueMMicrosoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmabilitytrue7System.ComponentModel.DefaultValueAttribute.IsSupportedtrue,System.Diagnostics.Metrics.Meter.IsSupportedfalse2System.Diagnostics.Tracing.EventSource.IsSupportedfalseSystem.Globalization.Invariantfalse)System.Net.Http.EnableActivityPropagationfalse$System.Net.Http.UseNativeHttpHandlertrue4System.Reflection.NullabilityInfoContext.IsSupportedtrue<System.Runtime.InteropServices.BuiltInComInterop.IsSupportedfalseESystem.Runtime.Serialization.EnableUnsafeBinaryFormatterSerializationfalse&System.StartupHookProvider.IsSupportedfalse-System.Text.Encoding.EnableUnsafeUTF7Encodingfalse<System.Text.Json.JsonSerializer.IsReflectionEnabledByDefaulttrue.Xamarin.Android.Net.UseNegotiateAuthenticationfalse/Switch.System.Reflection.ForceInterpretedInvoketrue=Microsoft.Extensions.DependencyInjection.DisableDynamicEnginetrue>Microsoft.Maui.RuntimeFeature.IsIVisualAssemblyScanningEnabledfalse=Microsoft.Maui.RuntimeFeature.AreBindingInterceptorsSupportedtrueHMicrosoft.Maui.RuntimeFeature.IsXamlCBindingWithSourceCompilationEnabledtrue