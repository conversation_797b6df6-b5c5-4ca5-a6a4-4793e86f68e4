package mono;
public class MonoPackageManager_Resources {
	public static String[] Assemblies = new String[]{
		/* We need to ensure that "PetSitterConnect.dll" comes first in this list. */
		"PetSitterConnect.dll",
		"CommunityToolkit.Maui.dll",
		"CommunityToolkit.Maui.Core.dll",
		"CommunityToolkit.Mvvm.dll",
		"GoogleGson.dll",
		"Microsoft.AspNetCore.Cryptography.Internal.dll",
		"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll",
		"Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll",
		"Microsoft.Data.Sqlite.dll",
		"Microsoft.EntityFrameworkCore.dll",
		"Microsoft.EntityFrameworkCore.Abstractions.dll",
		"Microsoft.EntityFrameworkCore.Relational.dll",
		"Microsoft.EntityFrameworkCore.Sqlite.dll",
		"Microsoft.Extensions.Caching.Abstractions.dll",
		"Microsoft.Extensions.Caching.Memory.dll",
		"Microsoft.Extensions.Configuration.dll",
		"Microsoft.Extensions.Configuration.Abstractions.dll",
		"Microsoft.Extensions.DependencyInjection.dll",
		"Microsoft.Extensions.DependencyInjection.Abstractions.dll",
		"Microsoft.Extensions.DependencyModel.dll",
		"Microsoft.Extensions.Identity.Core.dll",
		"Microsoft.Extensions.Identity.Stores.dll",
		"Microsoft.Extensions.Logging.dll",
		"Microsoft.Extensions.Logging.Abstractions.dll",
		"Microsoft.Extensions.Logging.Debug.dll",
		"Microsoft.Extensions.Options.dll",
		"Microsoft.Extensions.Primitives.dll",
		"Microsoft.Maui.Controls.dll",
		"Microsoft.Maui.Controls.Xaml.dll",
		"Microsoft.Maui.dll",
		"Microsoft.Maui.Essentials.dll",
		"Microsoft.Maui.Graphics.dll",
		"Newtonsoft.Json.dll",
		"SQLitePCLRaw.batteries_v2.dll",
		"SQLitePCLRaw.core.dll",
		"SQLitePCLRaw.lib.e_sqlite3.android.dll",
		"SQLitePCLRaw.provider.e_sqlite3.dll",
		"Xamarin.Android.Glide.dll",
		"Xamarin.Android.Glide.Annotations.dll",
		"Xamarin.Android.Glide.DiskLruCache.dll",
		"Xamarin.Android.Glide.GifDecoder.dll",
		"Xamarin.AndroidX.Activity.dll",
		"Xamarin.AndroidX.Activity.Ktx.dll",
		"Xamarin.AndroidX.Annotation.dll",
		"Xamarin.AndroidX.Annotation.Experimental.dll",
		"Xamarin.AndroidX.Annotation.Jvm.dll",
		"Xamarin.AndroidX.AppCompat.dll",
		"Xamarin.AndroidX.AppCompat.AppCompatResources.dll",
		"Xamarin.AndroidX.Arch.Core.Common.dll",
		"Xamarin.AndroidX.Arch.Core.Runtime.dll",
		"Xamarin.AndroidX.Browser.dll",
		"Xamarin.AndroidX.CardView.dll",
		"Xamarin.AndroidX.Collection.dll",
		"Xamarin.AndroidX.Collection.Jvm.dll",
		"Xamarin.AndroidX.Collection.Ktx.dll",
		"Xamarin.AndroidX.Concurrent.Futures.dll",
		"Xamarin.AndroidX.ConstraintLayout.dll",
		"Xamarin.AndroidX.ConstraintLayout.Core.dll",
		"Xamarin.AndroidX.CoordinatorLayout.dll",
		"Xamarin.AndroidX.Core.dll",
		"Xamarin.AndroidX.Core.Core.Ktx.dll",
		"Xamarin.AndroidX.CursorAdapter.dll",
		"Xamarin.AndroidX.CustomView.dll",
		"Xamarin.AndroidX.CustomView.PoolingContainer.dll",
		"Xamarin.AndroidX.DocumentFile.dll",
		"Xamarin.AndroidX.DrawerLayout.dll",
		"Xamarin.AndroidX.DynamicAnimation.dll",
		"Xamarin.AndroidX.Emoji2.dll",
		"Xamarin.AndroidX.Emoji2.ViewsHelper.dll",
		"Xamarin.AndroidX.ExifInterface.dll",
		"Xamarin.AndroidX.Fragment.dll",
		"Xamarin.AndroidX.Fragment.Ktx.dll",
		"Xamarin.AndroidX.Interpolator.dll",
		"Xamarin.AndroidX.Legacy.Support.Core.Utils.dll",
		"Xamarin.AndroidX.Lifecycle.Common.dll",
		"Xamarin.AndroidX.Lifecycle.Common.Jvm.dll",
		"Xamarin.AndroidX.Lifecycle.LiveData.dll",
		"Xamarin.AndroidX.Lifecycle.LiveData.Core.dll",
		"Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.dll",
		"Xamarin.AndroidX.Lifecycle.Process.dll",
		"Xamarin.AndroidX.Lifecycle.Runtime.dll",
		"Xamarin.AndroidX.Lifecycle.Runtime.Android.dll",
		"Xamarin.AndroidX.Lifecycle.Runtime.Ktx.dll",
		"Xamarin.AndroidX.Lifecycle.Runtime.Ktx.Android.dll",
		"Xamarin.AndroidX.Lifecycle.ViewModel.dll",
		"Xamarin.AndroidX.Lifecycle.ViewModel.Android.dll",
		"Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.dll",
		"Xamarin.AndroidX.Lifecycle.ViewModelSavedState.dll",
		"Xamarin.AndroidX.Loader.dll",
		"Xamarin.AndroidX.LocalBroadcastManager.dll",
		"Xamarin.AndroidX.Navigation.Common.dll",
		"Xamarin.AndroidX.Navigation.Fragment.dll",
		"Xamarin.AndroidX.Navigation.Runtime.dll",
		"Xamarin.AndroidX.Navigation.UI.dll",
		"Xamarin.AndroidX.Print.dll",
		"Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.dll",
		"Xamarin.AndroidX.RecyclerView.dll",
		"Xamarin.AndroidX.ResourceInspection.Annotation.dll",
		"Xamarin.AndroidX.SavedState.dll",
		"Xamarin.AndroidX.SavedState.SavedState.Ktx.dll",
		"Xamarin.AndroidX.Security.SecurityCrypto.dll",
		"Xamarin.AndroidX.SlidingPaneLayout.dll",
		"Xamarin.AndroidX.Startup.StartupRuntime.dll",
		"Xamarin.AndroidX.SwipeRefreshLayout.dll",
		"Xamarin.AndroidX.Tracing.Tracing.dll",
		"Xamarin.AndroidX.Transition.dll",
		"Xamarin.AndroidX.VectorDrawable.dll",
		"Xamarin.AndroidX.VectorDrawable.Animated.dll",
		"Xamarin.AndroidX.VersionedParcelable.dll",
		"Xamarin.AndroidX.ViewPager.dll",
		"Xamarin.AndroidX.ViewPager2.dll",
		"Xamarin.AndroidX.Window.dll",
		"Xamarin.AndroidX.Window.Extensions.Core.Core.dll",
		"Xamarin.Google.Android.Material.dll",
		"Jsr305Binding.dll",
		"Xamarin.Google.Crypto.Tink.Android.dll",
		"Xamarin.Google.ErrorProne.Annotations.dll",
		"Xamarin.Google.Guava.ListenableFuture.dll",
		"Xamarin.Jetbrains.Annotations.dll",
		"Xamarin.Kotlin.StdLib.dll",
		"Xamarin.KotlinX.AtomicFU.dll",
		"Xamarin.KotlinX.AtomicFU.Jvm.dll",
		"Xamarin.KotlinX.Coroutines.Android.dll",
		"Xamarin.KotlinX.Coroutines.Core.dll",
		"Xamarin.KotlinX.Coroutines.Core.Jvm.dll",
		"Xamarin.KotlinX.Serialization.Core.dll",
		"Xamarin.KotlinX.Serialization.Core.Jvm.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"Microsoft.Maui.Controls.resources.dll",
		"_Microsoft.Android.Resource.Designer.dll",
	};
	public static String[] Dependencies = new String[]{
	};
}
