﻿
//------------------------------------------------------------------------------
// <auto-generated>
//	This code was generated by a tool. DO NOT EDIT
// </auto-generated>
//------------------------------------------------------------------------------
// Use explicit namespaces for every type to avoid any conflicts with user project global usings
using Trace = global::System.Diagnostics.Trace;
using Type = global::System.Type;
using Assembly = global::System.Reflection.Assembly;
using MethodInfo = global::System.Reflection.MethodInfo;
using Task = global::System.Threading.Tasks.Task;
using Exception = global::System.Exception;
using FileNotFoundException = global::System.IO.FileNotFoundException;
using ModuleInitializerAttribute = global::System.Runtime.CompilerServices.ModuleInitializerAttribute;
using EditorBrowsableAttribute = global::System.ComponentModel.EditorBrowsableAttribute;
using EditorBrowsableState = global::System.ComponentModel.EditorBrowsableState;
using BindingFlags = global::System.Reflection.BindingFlags;

[EditorBrowsable (EditorBrowsableState.Never)]
static class HotReloadMobileTapInitializer
{
    static void LogError(string message) => Trace.WriteLine("Error: " + message);
    
    [ModuleInitializer]
    public static void LoadHotReloadBootstrapper()
    {
        try
        {
            Assembly assembly = typeof(HotReloadMobileTapInitializer).Assembly;

            string bootstrapAssemblyName = "Microsoft.VisualStudio.DesignTools.MobileTapContracts";
            Assembly agentBootstrapper = null;
            try
            {
               agentBootstrapper = Assembly.Load(bootstrapAssemblyName);
            }
            catch (FileNotFoundException)
            {
            }

            if (agentBootstrapper is null)
            {
                LogError($"Hot Reload bootstrap assembly not found and Hot Reload wil be disabled: {bootstrapAssemblyName}");
                return;
            }

#pragma warning disable IL2026 // Disable trimming related warning
            Type bootstrapper = agentBootstrapper.GetType("Microsoft.VisualStudio.DesignTools.MobileTapContracts.Bootstrapper");
#pragma warning restore IL2026
            if (bootstrapper == null)
            {
                LogError("Hot Reload bootstrap type not found: Microsoft.VisualStudio.DesignTools.MobileTapContracts.Bootstrapper");
                return;
            }

#pragma warning disable IL2075 // Disable trimming related warning
            MethodInfo method = bootstrapper.GetMethod("Boot", BindingFlags.Static | BindingFlags.Public);
#pragma warning restore IL2075
            if (method == null)
            {
                LogError("Hot Reload Bootstrapper.Boot method not found");
                return;
            }

            _ = Task.Run(() => {
               method.Invoke(bootstrapper, new object[]{false});
            });
        }
        catch(Exception e)
        {
            LogError($"Hot Reload bootstrap failed with exception:\n{e.Message}\n{e.StackTrace}");
        }
    }
}