﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a .NET MAUI source generator.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#nullable enable

namespace Microsoft.Maui.Controls.Generated
{
	using System.CodeDom.Compiler;

	[GeneratedCodeAttribute("Microsoft.Maui.Controls.BindingSourceGen, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null", "1.0.0.0")]
	internal static partial class GeneratedBindingInterceptors
	{
		private static bool ShouldUseSetter(BindingMode mode, BindableProperty bindableProperty)
			=> mode == BindingMode.OneWayToSource
				|| mode == BindingMode.TwoWay
				|| (mode == BindingMode.Default
					&& (bindableProperty.DefaultBindingMode == BindingMode.OneWayToSource
						|| bindableProperty.DefaultBindingMode == BindingMode.TwoWay));

		private static bool ShouldUseSetter(BindingMode mode)
			=> mode == BindingMode.OneWayToSource
				|| mode == BindingMode.TwoWay
				|| mode == BindingMode.Default;
	}
}