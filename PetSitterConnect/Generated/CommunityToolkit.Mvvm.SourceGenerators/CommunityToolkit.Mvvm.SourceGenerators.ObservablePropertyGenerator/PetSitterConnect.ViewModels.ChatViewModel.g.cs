﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class ChatViewModel
    {
        /// <inheritdoc cref="bookingId"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public int BookingId
        {
            get => bookingId;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<int>.Default.Equals(bookingId, value))
                {
                    int __oldValue = bookingId;
                    OnBookingIdChanging(value);
                    OnBookingIdChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.BookingId);
                    bookingId = value;
                    OnBookingIdChanged(value);
                    OnBookingIdChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.BookingId);
                }
            }
        }

        /// <inheritdoc cref="messages"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.ChatMessage> Messages
        {
            get => messages;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("messages")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.ChatMessage>>.Default.Equals(messages, value))
                {
                    global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.ChatMessage>? __oldValue = messages;
                    OnMessagesChanging(value);
                    OnMessagesChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Messages);
                    messages = value;
                    OnMessagesChanged(value);
                    OnMessagesChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Messages);
                }
            }
        }

        /// <inheritdoc cref="messageText"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string MessageText
        {
            get => messageText;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("messageText")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(messageText, value))
                {
                    string? __oldValue = messageText;
                    OnMessageTextChanging(value);
                    OnMessageTextChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.MessageText);
                    messageText = value;
                    OnMessageTextChanged(value);
                    OnMessageTextChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.MessageText);
                }
            }
        }

        /// <inheritdoc cref="currentUser"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.User? CurrentUser
        {
            get => currentUser;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.User?>.Default.Equals(currentUser, value))
                {
                    global::PetSitterConnect.Models.User? __oldValue = currentUser;
                    OnCurrentUserChanging(value);
                    OnCurrentUserChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CurrentUser);
                    currentUser = value;
                    OnCurrentUserChanged(value);
                    OnCurrentUserChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CurrentUser);
                }
            }
        }

        /// <inheritdoc cref="booking"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.Booking? Booking
        {
            get => booking;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.Booking?>.Default.Equals(booking, value))
                {
                    global::PetSitterConnect.Models.Booking? __oldValue = booking;
                    OnBookingChanging(value);
                    OnBookingChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Booking);
                    booking = value;
                    OnBookingChanged(value);
                    OnBookingChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Booking);
                }
            }
        }

        /// <inheritdoc cref="otherParticipantName"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string OtherParticipantName
        {
            get => otherParticipantName;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("otherParticipantName")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(otherParticipantName, value))
                {
                    string? __oldValue = otherParticipantName;
                    OnOtherParticipantNameChanging(value);
                    OnOtherParticipantNameChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.OtherParticipantName);
                    otherParticipantName = value;
                    OnOtherParticipantNameChanged(value);
                    OnOtherParticipantNameChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.OtherParticipantName);
                }
            }
        }

        /// <inheritdoc cref="chatTitle"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ChatTitle
        {
            get => chatTitle;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("chatTitle")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(chatTitle, value))
                {
                    string? __oldValue = chatTitle;
                    OnChatTitleChanging(value);
                    OnChatTitleChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ChatTitle);
                    chatTitle = value;
                    OnChatTitleChanged(value);
                    OnChatTitleChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ChatTitle);
                }
            }
        }

        /// <inheritdoc cref="canSendMessage"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool CanSendMessage
        {
            get => canSendMessage;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(canSendMessage, value))
                {
                    bool __oldValue = canSendMessage;
                    OnCanSendMessageChanging(value);
                    OnCanSendMessageChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanSendMessage);
                    canSendMessage = value;
                    OnCanSendMessageChanged(value);
                    OnCanSendMessageChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanSendMessage);
                }
            }
        }

        /// <inheritdoc cref="isTyping"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsTyping
        {
            get => isTyping;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isTyping, value))
                {
                    bool __oldValue = isTyping;
                    OnIsTypingChanging(value);
                    OnIsTypingChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsTyping);
                    isTyping = value;
                    OnIsTypingChanged(value);
                    OnIsTypingChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsTyping);
                }
            }
        }

        /// <inheritdoc cref="userRoleIcon"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string UserRoleIcon
        {
            get => userRoleIcon;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleIcon")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(userRoleIcon, value))
                {
                    string? __oldValue = userRoleIcon;
                    OnUserRoleIconChanging(value);
                    OnUserRoleIconChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleIcon);
                    userRoleIcon = value;
                    OnUserRoleIconChanged(value);
                    OnUserRoleIconChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleIcon);
                }
            }
        }

        /// <inheritdoc cref="userRoleColor"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::Microsoft.Maui.Graphics.Color UserRoleColor
        {
            get => userRoleColor;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleColor")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::Microsoft.Maui.Graphics.Color>.Default.Equals(userRoleColor, value))
                {
                    global::Microsoft.Maui.Graphics.Color? __oldValue = userRoleColor;
                    OnUserRoleColorChanging(value);
                    OnUserRoleColorChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleColor);
                    userRoleColor = value;
                    OnUserRoleColorChanged(value);
                    OnUserRoleColorChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleColor);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="BookingId"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="BookingId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBookingIdChanging(int value);
        /// <summary>Executes the logic for when <see cref="BookingId"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="BookingId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBookingIdChanging(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="BookingId"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="BookingId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBookingIdChanged(int value);
        /// <summary>Executes the logic for when <see cref="BookingId"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="BookingId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBookingIdChanged(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="Messages"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Messages"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMessagesChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.ChatMessage> value);
        /// <summary>Executes the logic for when <see cref="Messages"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Messages"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMessagesChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.ChatMessage>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.ChatMessage> newValue);
        /// <summary>Executes the logic for when <see cref="Messages"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Messages"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMessagesChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.ChatMessage> value);
        /// <summary>Executes the logic for when <see cref="Messages"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Messages"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMessagesChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.ChatMessage>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.ChatMessage> newValue);
        /// <summary>Executes the logic for when <see cref="MessageText"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="MessageText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMessageTextChanging(string value);
        /// <summary>Executes the logic for when <see cref="MessageText"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="MessageText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMessageTextChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="MessageText"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="MessageText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMessageTextChanged(string value);
        /// <summary>Executes the logic for when <see cref="MessageText"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="MessageText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMessageTextChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="Booking"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Booking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBookingChanging(global::PetSitterConnect.Models.Booking? value);
        /// <summary>Executes the logic for when <see cref="Booking"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Booking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBookingChanging(global::PetSitterConnect.Models.Booking? oldValue, global::PetSitterConnect.Models.Booking? newValue);
        /// <summary>Executes the logic for when <see cref="Booking"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Booking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBookingChanged(global::PetSitterConnect.Models.Booking? value);
        /// <summary>Executes the logic for when <see cref="Booking"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Booking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBookingChanged(global::PetSitterConnect.Models.Booking? oldValue, global::PetSitterConnect.Models.Booking? newValue);
        /// <summary>Executes the logic for when <see cref="OtherParticipantName"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="OtherParticipantName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnOtherParticipantNameChanging(string value);
        /// <summary>Executes the logic for when <see cref="OtherParticipantName"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="OtherParticipantName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnOtherParticipantNameChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="OtherParticipantName"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="OtherParticipantName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnOtherParticipantNameChanged(string value);
        /// <summary>Executes the logic for when <see cref="OtherParticipantName"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="OtherParticipantName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnOtherParticipantNameChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ChatTitle"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ChatTitle"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnChatTitleChanging(string value);
        /// <summary>Executes the logic for when <see cref="ChatTitle"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ChatTitle"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnChatTitleChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ChatTitle"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ChatTitle"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnChatTitleChanged(string value);
        /// <summary>Executes the logic for when <see cref="ChatTitle"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ChatTitle"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnChatTitleChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="CanSendMessage"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CanSendMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCanSendMessageChanging(bool value);
        /// <summary>Executes the logic for when <see cref="CanSendMessage"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CanSendMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCanSendMessageChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="CanSendMessage"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CanSendMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCanSendMessageChanged(bool value);
        /// <summary>Executes the logic for when <see cref="CanSendMessage"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CanSendMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCanSendMessageChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsTyping"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsTyping"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsTypingChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsTyping"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsTyping"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsTypingChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsTyping"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsTyping"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsTypingChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsTyping"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsTyping"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsTypingChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleIconChanging(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleIconChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleIconChanged(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleIconChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleColorChanging(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleColorChanging(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleColorChanged(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleColorChanged(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
    }
}