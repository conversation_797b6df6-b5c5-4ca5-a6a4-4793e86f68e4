﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class ChatListViewModel
    {
        /// <inheritdoc cref="conversations"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Services.ChatConversation> Conversations
        {
            get => conversations;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("conversations")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Services.ChatConversation>>.Default.Equals(conversations, value))
                {
                    global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Services.ChatConversation>? __oldValue = conversations;
                    OnConversationsChanging(value);
                    OnConversationsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Conversations);
                    conversations = value;
                    OnConversationsChanged(value);
                    OnConversationsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Conversations);
                }
            }
        }

        /// <inheritdoc cref="currentUser"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.User? CurrentUser
        {
            get => currentUser;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.User?>.Default.Equals(currentUser, value))
                {
                    global::PetSitterConnect.Models.User? __oldValue = currentUser;
                    OnCurrentUserChanging(value);
                    OnCurrentUserChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CurrentUser);
                    currentUser = value;
                    OnCurrentUserChanged(value);
                    OnCurrentUserChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CurrentUser);
                }
            }
        }

        /// <inheritdoc cref="selectedConversation"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Services.ChatConversation? SelectedConversation
        {
            get => selectedConversation;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Services.ChatConversation?>.Default.Equals(selectedConversation, value))
                {
                    global::PetSitterConnect.Services.ChatConversation? __oldValue = selectedConversation;
                    OnSelectedConversationChanging(value);
                    OnSelectedConversationChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedConversation);
                    selectedConversation = value;
                    OnSelectedConversationChanged(value);
                    OnSelectedConversationChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedConversation);
                }
            }
        }

        /// <inheritdoc cref="hasConversations"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool HasConversations
        {
            get => hasConversations;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(hasConversations, value))
                {
                    bool __oldValue = hasConversations;
                    OnHasConversationsChanging(value);
                    OnHasConversationsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.HasConversations);
                    hasConversations = value;
                    OnHasConversationsChanged(value);
                    OnHasConversationsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.HasConversations);
                }
            }
        }

        /// <inheritdoc cref="userRoleIcon"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string UserRoleIcon
        {
            get => userRoleIcon;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleIcon")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(userRoleIcon, value))
                {
                    string? __oldValue = userRoleIcon;
                    OnUserRoleIconChanging(value);
                    OnUserRoleIconChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleIcon);
                    userRoleIcon = value;
                    OnUserRoleIconChanged(value);
                    OnUserRoleIconChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleIcon);
                }
            }
        }

        /// <inheritdoc cref="userRoleLabel"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string UserRoleLabel
        {
            get => userRoleLabel;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleLabel")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(userRoleLabel, value))
                {
                    string? __oldValue = userRoleLabel;
                    OnUserRoleLabelChanging(value);
                    OnUserRoleLabelChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleLabel);
                    userRoleLabel = value;
                    OnUserRoleLabelChanged(value);
                    OnUserRoleLabelChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleLabel);
                }
            }
        }

        /// <inheritdoc cref="userRoleColor"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::Microsoft.Maui.Graphics.Color UserRoleColor
        {
            get => userRoleColor;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleColor")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::Microsoft.Maui.Graphics.Color>.Default.Equals(userRoleColor, value))
                {
                    global::Microsoft.Maui.Graphics.Color? __oldValue = userRoleColor;
                    OnUserRoleColorChanging(value);
                    OnUserRoleColorChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleColor);
                    userRoleColor = value;
                    OnUserRoleColorChanged(value);
                    OnUserRoleColorChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleColor);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="Conversations"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Conversations"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnConversationsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Services.ChatConversation> value);
        /// <summary>Executes the logic for when <see cref="Conversations"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Conversations"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnConversationsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Services.ChatConversation>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Services.ChatConversation> newValue);
        /// <summary>Executes the logic for when <see cref="Conversations"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Conversations"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnConversationsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Services.ChatConversation> value);
        /// <summary>Executes the logic for when <see cref="Conversations"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Conversations"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnConversationsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Services.ChatConversation>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Services.ChatConversation> newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="SelectedConversation"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedConversation"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedConversationChanging(global::PetSitterConnect.Services.ChatConversation? value);
        /// <summary>Executes the logic for when <see cref="SelectedConversation"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedConversation"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedConversationChanging(global::PetSitterConnect.Services.ChatConversation? oldValue, global::PetSitterConnect.Services.ChatConversation? newValue);
        /// <summary>Executes the logic for when <see cref="SelectedConversation"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedConversation"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedConversationChanged(global::PetSitterConnect.Services.ChatConversation? value);
        /// <summary>Executes the logic for when <see cref="SelectedConversation"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedConversation"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedConversationChanged(global::PetSitterConnect.Services.ChatConversation? oldValue, global::PetSitterConnect.Services.ChatConversation? newValue);
        /// <summary>Executes the logic for when <see cref="HasConversations"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="HasConversations"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasConversationsChanging(bool value);
        /// <summary>Executes the logic for when <see cref="HasConversations"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="HasConversations"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasConversationsChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="HasConversations"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="HasConversations"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasConversationsChanged(bool value);
        /// <summary>Executes the logic for when <see cref="HasConversations"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="HasConversations"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasConversationsChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanging(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanged(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanging(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanged(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanging(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanging(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanged(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanged(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
    }
}