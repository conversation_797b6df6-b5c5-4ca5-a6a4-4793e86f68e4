﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class PetCareRequestListViewModel
    {
        /// <inheritdoc cref="petCareRequests"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest> PetCareRequests
        {
            get => petCareRequests;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("petCareRequests")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest>>.Default.Equals(petCareRequests, value))
                {
                    global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest>? __oldValue = petCareRequests;
                    OnPetCareRequestsChanging(value);
                    OnPetCareRequestsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PetCareRequests);
                    petCareRequests = value;
                    OnPetCareRequestsChanged(value);
                    OnPetCareRequestsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PetCareRequests);
                }
            }
        }

        /// <inheritdoc cref="filteredRequests"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest> FilteredRequests
        {
            get => filteredRequests;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("filteredRequests")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest>>.Default.Equals(filteredRequests, value))
                {
                    global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest>? __oldValue = filteredRequests;
                    OnFilteredRequestsChanging(value);
                    OnFilteredRequestsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FilteredRequests);
                    filteredRequests = value;
                    OnFilteredRequestsChanged(value);
                    OnFilteredRequestsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FilteredRequests);
                }
            }
        }

        /// <inheritdoc cref="searchText"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string SearchText
        {
            get => searchText;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("searchText")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(searchText, value))
                {
                    string? __oldValue = searchText;
                    OnSearchTextChanging(value);
                    OnSearchTextChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SearchText);
                    searchText = value;
                    OnSearchTextChanged(value);
                    OnSearchTextChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SearchText);
                }
            }
        }

        /// <inheritdoc cref="selectedCareType"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.CareType? SelectedCareType
        {
            get => selectedCareType;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.CareType?>.Default.Equals(selectedCareType, value))
                {
                    global::PetSitterConnect.Models.CareType? __oldValue = selectedCareType;
                    OnSelectedCareTypeChanging(value);
                    OnSelectedCareTypeChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedCareType);
                    selectedCareType = value;
                    OnSelectedCareTypeChanged(value);
                    OnSelectedCareTypeChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedCareType);
                }
            }
        }

        /// <inheritdoc cref="showMyRequestsOnly"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool ShowMyRequestsOnly
        {
            get => showMyRequestsOnly;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(showMyRequestsOnly, value))
                {
                    bool __oldValue = showMyRequestsOnly;
                    OnShowMyRequestsOnlyChanging(value);
                    OnShowMyRequestsOnlyChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ShowMyRequestsOnly);
                    showMyRequestsOnly = value;
                    OnShowMyRequestsOnlyChanged(value);
                    OnShowMyRequestsOnlyChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ShowMyRequestsOnly);
                }
            }
        }

        /// <inheritdoc cref="selectedRequest"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.PetCareRequest? SelectedRequest
        {
            get => selectedRequest;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.PetCareRequest?>.Default.Equals(selectedRequest, value))
                {
                    global::PetSitterConnect.Models.PetCareRequest? __oldValue = selectedRequest;
                    OnSelectedRequestChanging(value);
                    OnSelectedRequestChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedRequest);
                    selectedRequest = value;
                    OnSelectedRequestChanged(value);
                    OnSelectedRequestChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedRequest);
                }
            }
        }

        /// <inheritdoc cref="currentUser"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.User? CurrentUser
        {
            get => currentUser;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.User?>.Default.Equals(currentUser, value))
                {
                    global::PetSitterConnect.Models.User? __oldValue = currentUser;
                    OnCurrentUserChanging(value);
                    OnCurrentUserChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CurrentUser);
                    currentUser = value;
                    OnCurrentUserChanged(value);
                    OnCurrentUserChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CurrentUser);
                }
            }
        }

        /// <inheritdoc cref="userRoleIcon"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string UserRoleIcon
        {
            get => userRoleIcon;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleIcon")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(userRoleIcon, value))
                {
                    string? __oldValue = userRoleIcon;
                    OnUserRoleIconChanging(value);
                    OnUserRoleIconChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleIcon);
                    userRoleIcon = value;
                    OnUserRoleIconChanged(value);
                    OnUserRoleIconChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleIcon);
                }
            }
        }

        /// <inheritdoc cref="userRoleLabel"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string UserRoleLabel
        {
            get => userRoleLabel;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleLabel")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(userRoleLabel, value))
                {
                    string? __oldValue = userRoleLabel;
                    OnUserRoleLabelChanging(value);
                    OnUserRoleLabelChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleLabel);
                    userRoleLabel = value;
                    OnUserRoleLabelChanged(value);
                    OnUserRoleLabelChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleLabel);
                }
            }
        }

        /// <inheritdoc cref="userRoleColor"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::Microsoft.Maui.Graphics.Color UserRoleColor
        {
            get => userRoleColor;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleColor")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::Microsoft.Maui.Graphics.Color>.Default.Equals(userRoleColor, value))
                {
                    global::Microsoft.Maui.Graphics.Color? __oldValue = userRoleColor;
                    OnUserRoleColorChanging(value);
                    OnUserRoleColorChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleColor);
                    userRoleColor = value;
                    OnUserRoleColorChanged(value);
                    OnUserRoleColorChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleColor);
                }
            }
        }

        /// <inheritdoc cref="canCreateRequests"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool CanCreateRequests
        {
            get => canCreateRequests;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(canCreateRequests, value))
                {
                    bool __oldValue = canCreateRequests;
                    OnCanCreateRequestsChanging(value);
                    OnCanCreateRequestsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanCreateRequests);
                    canCreateRequests = value;
                    OnCanCreateRequestsChanged(value);
                    OnCanCreateRequestsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanCreateRequests);
                }
            }
        }

        /// <inheritdoc cref="contextDescription"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ContextDescription
        {
            get => contextDescription;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("contextDescription")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(contextDescription, value))
                {
                    string? __oldValue = contextDescription;
                    OnContextDescriptionChanging(value);
                    OnContextDescriptionChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ContextDescription);
                    contextDescription = value;
                    OnContextDescriptionChanged(value);
                    OnContextDescriptionChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ContextDescription);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="PetCareRequests"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="PetCareRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPetCareRequestsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest> value);
        /// <summary>Executes the logic for when <see cref="PetCareRequests"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="PetCareRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPetCareRequestsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest> newValue);
        /// <summary>Executes the logic for when <see cref="PetCareRequests"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="PetCareRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPetCareRequestsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest> value);
        /// <summary>Executes the logic for when <see cref="PetCareRequests"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="PetCareRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPetCareRequestsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest> newValue);
        /// <summary>Executes the logic for when <see cref="FilteredRequests"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="FilteredRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFilteredRequestsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest> value);
        /// <summary>Executes the logic for when <see cref="FilteredRequests"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="FilteredRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFilteredRequestsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest> newValue);
        /// <summary>Executes the logic for when <see cref="FilteredRequests"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="FilteredRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFilteredRequestsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest> value);
        /// <summary>Executes the logic for when <see cref="FilteredRequests"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="FilteredRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFilteredRequestsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.PetCareRequest> newValue);
        /// <summary>Executes the logic for when <see cref="SearchText"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SearchText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSearchTextChanging(string value);
        /// <summary>Executes the logic for when <see cref="SearchText"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SearchText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSearchTextChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="SearchText"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SearchText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSearchTextChanged(string value);
        /// <summary>Executes the logic for when <see cref="SearchText"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SearchText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSearchTextChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="SelectedCareType"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedCareType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedCareTypeChanging(global::PetSitterConnect.Models.CareType? value);
        /// <summary>Executes the logic for when <see cref="SelectedCareType"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedCareType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedCareTypeChanging(global::PetSitterConnect.Models.CareType? oldValue, global::PetSitterConnect.Models.CareType? newValue);
        /// <summary>Executes the logic for when <see cref="SelectedCareType"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedCareType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedCareTypeChanged(global::PetSitterConnect.Models.CareType? value);
        /// <summary>Executes the logic for when <see cref="SelectedCareType"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedCareType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedCareTypeChanged(global::PetSitterConnect.Models.CareType? oldValue, global::PetSitterConnect.Models.CareType? newValue);
        /// <summary>Executes the logic for when <see cref="ShowMyRequestsOnly"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ShowMyRequestsOnly"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnShowMyRequestsOnlyChanging(bool value);
        /// <summary>Executes the logic for when <see cref="ShowMyRequestsOnly"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ShowMyRequestsOnly"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnShowMyRequestsOnlyChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="ShowMyRequestsOnly"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ShowMyRequestsOnly"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnShowMyRequestsOnlyChanged(bool value);
        /// <summary>Executes the logic for when <see cref="ShowMyRequestsOnly"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ShowMyRequestsOnly"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnShowMyRequestsOnlyChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="SelectedRequest"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedRequestChanging(global::PetSitterConnect.Models.PetCareRequest? value);
        /// <summary>Executes the logic for when <see cref="SelectedRequest"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedRequestChanging(global::PetSitterConnect.Models.PetCareRequest? oldValue, global::PetSitterConnect.Models.PetCareRequest? newValue);
        /// <summary>Executes the logic for when <see cref="SelectedRequest"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedRequestChanged(global::PetSitterConnect.Models.PetCareRequest? value);
        /// <summary>Executes the logic for when <see cref="SelectedRequest"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedRequestChanged(global::PetSitterConnect.Models.PetCareRequest? oldValue, global::PetSitterConnect.Models.PetCareRequest? newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleIconChanging(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleIconChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleIconChanged(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleIconChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleLabelChanging(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleLabelChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleLabelChanged(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleLabelChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleColorChanging(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleColorChanging(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleColorChanged(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserRoleColorChanged(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
        /// <summary>Executes the logic for when <see cref="CanCreateRequests"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CanCreateRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCanCreateRequestsChanging(bool value);
        /// <summary>Executes the logic for when <see cref="CanCreateRequests"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CanCreateRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCanCreateRequestsChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="CanCreateRequests"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CanCreateRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCanCreateRequestsChanged(bool value);
        /// <summary>Executes the logic for when <see cref="CanCreateRequests"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CanCreateRequests"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCanCreateRequestsChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="ContextDescription"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ContextDescription"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnContextDescriptionChanging(string value);
        /// <summary>Executes the logic for when <see cref="ContextDescription"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ContextDescription"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnContextDescriptionChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ContextDescription"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ContextDescription"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnContextDescriptionChanged(string value);
        /// <summary>Executes the logic for when <see cref="ContextDescription"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ContextDescription"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnContextDescriptionChanged(string? oldValue, string newValue);
    }
}