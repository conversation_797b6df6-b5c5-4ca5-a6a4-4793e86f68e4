﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class RegisterViewModel
    {
        /// <inheritdoc cref="firstName"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string FirstName
        {
            get => firstName;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("firstName")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(firstName, value))
                {
                    string? __oldValue = firstName;
                    OnFirstNameChanging(value);
                    OnFirstNameChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FirstName);
                    firstName = value;
                    OnFirstNameChanged(value);
                    OnFirstNameChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FirstName);
                }
            }
        }

        /// <inheritdoc cref="lastName"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string LastName
        {
            get => lastName;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("lastName")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(lastName, value))
                {
                    string? __oldValue = lastName;
                    OnLastNameChanging(value);
                    OnLastNameChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.LastName);
                    lastName = value;
                    OnLastNameChanged(value);
                    OnLastNameChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.LastName);
                }
            }
        }

        /// <inheritdoc cref="email"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Email
        {
            get => email;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("email")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(email, value))
                {
                    string? __oldValue = email;
                    OnEmailChanging(value);
                    OnEmailChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Email);
                    email = value;
                    OnEmailChanged(value);
                    OnEmailChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Email);
                }
            }
        }

        /// <inheritdoc cref="password"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Password
        {
            get => password;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("password")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(password, value))
                {
                    string? __oldValue = password;
                    OnPasswordChanging(value);
                    OnPasswordChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Password);
                    password = value;
                    OnPasswordChanged(value);
                    OnPasswordChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Password);
                }
            }
        }

        /// <inheritdoc cref="confirmPassword"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ConfirmPassword
        {
            get => confirmPassword;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("confirmPassword")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(confirmPassword, value))
                {
                    string? __oldValue = confirmPassword;
                    OnConfirmPasswordChanging(value);
                    OnConfirmPasswordChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ConfirmPassword);
                    confirmPassword = value;
                    OnConfirmPasswordChanged(value);
                    OnConfirmPasswordChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ConfirmPassword);
                }
            }
        }

        /// <inheritdoc cref="userType"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.UserType UserType
        {
            get => userType;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.UserType>.Default.Equals(userType, value))
                {
                    global::PetSitterConnect.Models.UserType __oldValue = userType;
                    OnUserTypeChanging(value);
                    OnUserTypeChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserType);
                    userType = value;
                    OnUserTypeChanged(value);
                    OnUserTypeChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserType);
                }
            }
        }

        /// <inheritdoc cref="phoneNumber"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string PhoneNumber
        {
            get => phoneNumber;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("phoneNumber")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(phoneNumber, value))
                {
                    string? __oldValue = phoneNumber;
                    OnPhoneNumberChanging(value);
                    OnPhoneNumberChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PhoneNumber);
                    phoneNumber = value;
                    OnPhoneNumberChanged(value);
                    OnPhoneNumberChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PhoneNumber);
                }
            }
        }

        /// <inheritdoc cref="address"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Address
        {
            get => address;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("address")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(address, value))
                {
                    string? __oldValue = address;
                    OnAddressChanging(value);
                    OnAddressChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Address);
                    address = value;
                    OnAddressChanged(value);
                    OnAddressChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Address);
                }
            }
        }

        /// <inheritdoc cref="city"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string City
        {
            get => city;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("city")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(city, value))
                {
                    string? __oldValue = city;
                    OnCityChanging(value);
                    OnCityChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.City);
                    city = value;
                    OnCityChanged(value);
                    OnCityChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.City);
                }
            }
        }

        /// <inheritdoc cref="postalCode"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string PostalCode
        {
            get => postalCode;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("postalCode")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(postalCode, value))
                {
                    string? __oldValue = postalCode;
                    OnPostalCodeChanging(value);
                    OnPostalCodeChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PostalCode);
                    postalCode = value;
                    OnPostalCodeChanged(value);
                    OnPostalCodeChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PostalCode);
                }
            }
        }

        /// <inheritdoc cref="country"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Country
        {
            get => country;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("country")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(country, value))
                {
                    string? __oldValue = country;
                    OnCountryChanging(value);
                    OnCountryChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Country);
                    country = value;
                    OnCountryChanged(value);
                    OnCountryChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Country);
                }
            }
        }

        /// <inheritdoc cref="errorMessage"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ErrorMessage
        {
            get => errorMessage;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("errorMessage")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(errorMessage, value))
                {
                    string? __oldValue = errorMessage;
                    OnErrorMessageChanging(value);
                    OnErrorMessageChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ErrorMessage);
                    errorMessage = value;
                    OnErrorMessageChanged(value);
                    OnErrorMessageChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ErrorMessage);
                }
            }
        }

        /// <inheritdoc cref="acceptTerms"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool AcceptTerms
        {
            get => acceptTerms;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(acceptTerms, value))
                {
                    bool __oldValue = acceptTerms;
                    OnAcceptTermsChanging(value);
                    OnAcceptTermsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.AcceptTerms);
                    acceptTerms = value;
                    OnAcceptTermsChanged(value);
                    OnAcceptTermsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.AcceptTerms);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="FirstName"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="FirstName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFirstNameChanging(string value);
        /// <summary>Executes the logic for when <see cref="FirstName"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="FirstName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFirstNameChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="FirstName"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="FirstName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFirstNameChanged(string value);
        /// <summary>Executes the logic for when <see cref="FirstName"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="FirstName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFirstNameChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="LastName"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="LastName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnLastNameChanging(string value);
        /// <summary>Executes the logic for when <see cref="LastName"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="LastName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnLastNameChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="LastName"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="LastName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnLastNameChanged(string value);
        /// <summary>Executes the logic for when <see cref="LastName"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="LastName"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnLastNameChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Email"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Email"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnEmailChanging(string value);
        /// <summary>Executes the logic for when <see cref="Email"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Email"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnEmailChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Email"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Email"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnEmailChanged(string value);
        /// <summary>Executes the logic for when <see cref="Email"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Email"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnEmailChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Password"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Password"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPasswordChanging(string value);
        /// <summary>Executes the logic for when <see cref="Password"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Password"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPasswordChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Password"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Password"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPasswordChanged(string value);
        /// <summary>Executes the logic for when <see cref="Password"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Password"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPasswordChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ConfirmPassword"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ConfirmPassword"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnConfirmPasswordChanging(string value);
        /// <summary>Executes the logic for when <see cref="ConfirmPassword"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ConfirmPassword"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnConfirmPasswordChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ConfirmPassword"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ConfirmPassword"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnConfirmPasswordChanged(string value);
        /// <summary>Executes the logic for when <see cref="ConfirmPassword"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ConfirmPassword"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnConfirmPasswordChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserType"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserTypeChanging(global::PetSitterConnect.Models.UserType value);
        /// <summary>Executes the logic for when <see cref="UserType"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserTypeChanging(global::PetSitterConnect.Models.UserType oldValue, global::PetSitterConnect.Models.UserType newValue);
        /// <summary>Executes the logic for when <see cref="UserType"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserTypeChanged(global::PetSitterConnect.Models.UserType value);
        /// <summary>Executes the logic for when <see cref="UserType"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserTypeChanged(global::PetSitterConnect.Models.UserType oldValue, global::PetSitterConnect.Models.UserType newValue);
        /// <summary>Executes the logic for when <see cref="PhoneNumber"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="PhoneNumber"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPhoneNumberChanging(string value);
        /// <summary>Executes the logic for when <see cref="PhoneNumber"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="PhoneNumber"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPhoneNumberChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="PhoneNumber"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="PhoneNumber"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPhoneNumberChanged(string value);
        /// <summary>Executes the logic for when <see cref="PhoneNumber"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="PhoneNumber"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPhoneNumberChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Address"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Address"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAddressChanging(string value);
        /// <summary>Executes the logic for when <see cref="Address"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Address"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAddressChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Address"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Address"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAddressChanged(string value);
        /// <summary>Executes the logic for when <see cref="Address"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Address"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAddressChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="City"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="City"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCityChanging(string value);
        /// <summary>Executes the logic for when <see cref="City"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="City"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCityChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="City"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="City"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCityChanged(string value);
        /// <summary>Executes the logic for when <see cref="City"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="City"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCityChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="PostalCode"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="PostalCode"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPostalCodeChanging(string value);
        /// <summary>Executes the logic for when <see cref="PostalCode"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="PostalCode"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPostalCodeChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="PostalCode"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="PostalCode"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPostalCodeChanged(string value);
        /// <summary>Executes the logic for when <see cref="PostalCode"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="PostalCode"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnPostalCodeChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Country"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Country"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCountryChanging(string value);
        /// <summary>Executes the logic for when <see cref="Country"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Country"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCountryChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Country"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Country"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCountryChanged(string value);
        /// <summary>Executes the logic for when <see cref="Country"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Country"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCountryChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanging(string value);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanged(string value);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="AcceptTerms"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="AcceptTerms"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAcceptTermsChanging(bool value);
        /// <summary>Executes the logic for when <see cref="AcceptTerms"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="AcceptTerms"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAcceptTermsChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="AcceptTerms"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="AcceptTerms"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAcceptTermsChanged(bool value);
        /// <summary>Executes the logic for when <see cref="AcceptTerms"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="AcceptTerms"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAcceptTermsChanged(bool oldValue, bool newValue);
    }
}