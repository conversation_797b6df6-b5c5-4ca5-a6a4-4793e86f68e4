﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class CreatePetCareRequestViewModel
    {
        /// <inheritdoc cref="userPets"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Pet> UserPets
        {
            get => userPets;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userPets")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Pet>>.Default.Equals(userPets, value))
                {
                    global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Pet>? __oldValue = userPets;
                    OnUserPetsChanging(value);
                    OnUserPetsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserPets);
                    userPets = value;
                    OnUserPetsChanged(value);
                    OnUserPetsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserPets);
                }
            }
        }

        /// <inheritdoc cref="selectedPet"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.Pet? SelectedPet
        {
            get => selectedPet;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.Pet?>.Default.Equals(selectedPet, value))
                {
                    global::PetSitterConnect.Models.Pet? __oldValue = selectedPet;
                    OnSelectedPetChanging(value);
                    OnSelectedPetChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedPet);
                    selectedPet = value;
                    OnSelectedPetChanged(value);
                    OnSelectedPetChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedPet);
                }
            }
        }

        /// <inheritdoc cref="title"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Title
        {
            get => title;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("title")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(title, value))
                {
                    string? __oldValue = title;
                    OnTitleChanging(value);
                    OnTitleChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Title);
                    title = value;
                    OnTitleChanged(value);
                    OnTitleChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Title);
                }
            }
        }

        /// <inheritdoc cref="description"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Description
        {
            get => description;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("description")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(description, value))
                {
                    string? __oldValue = description;
                    OnDescriptionChanging(value);
                    OnDescriptionChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Description);
                    description = value;
                    OnDescriptionChanged(value);
                    OnDescriptionChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Description);
                }
            }
        }

        /// <inheritdoc cref="startDate"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.DateTime StartDate
        {
            get => startDate;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.DateTime>.Default.Equals(startDate, value))
                {
                    global::System.DateTime __oldValue = startDate;
                    OnStartDateChanging(value);
                    OnStartDateChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.StartDate);
                    startDate = value;
                    OnStartDateChanged(value);
                    OnStartDateChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.StartDate);
                }
            }
        }

        /// <inheritdoc cref="endDate"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.DateTime EndDate
        {
            get => endDate;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.DateTime>.Default.Equals(endDate, value))
                {
                    global::System.DateTime __oldValue = endDate;
                    OnEndDateChanging(value);
                    OnEndDateChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.EndDate);
                    endDate = value;
                    OnEndDateChanged(value);
                    OnEndDateChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.EndDate);
                }
            }
        }

        /// <inheritdoc cref="careType"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.CareType CareType
        {
            get => careType;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.CareType>.Default.Equals(careType, value))
                {
                    global::PetSitterConnect.Models.CareType __oldValue = careType;
                    OnCareTypeChanging(value);
                    OnCareTypeChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CareType);
                    careType = value;
                    OnCareTypeChanged(value);
                    OnCareTypeChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CareType);
                }
            }
        }

        /// <inheritdoc cref="budget"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public decimal Budget
        {
            get => budget;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<decimal>.Default.Equals(budget, value))
                {
                    decimal __oldValue = budget;
                    OnBudgetChanging(value);
                    OnBudgetChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Budget);
                    budget = value;
                    OnBudgetChanged(value);
                    OnBudgetChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Budget);
                }
            }
        }

        /// <inheritdoc cref="location"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Location
        {
            get => location;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("location")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(location, value))
                {
                    string? __oldValue = location;
                    OnLocationChanging(value);
                    OnLocationChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Location);
                    location = value;
                    OnLocationChanged(value);
                    OnLocationChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Location);
                }
            }
        }

        /// <inheritdoc cref="specialInstructions"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string SpecialInstructions
        {
            get => specialInstructions;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("specialInstructions")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(specialInstructions, value))
                {
                    string? __oldValue = specialInstructions;
                    OnSpecialInstructionsChanging(value);
                    OnSpecialInstructionsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SpecialInstructions);
                    specialInstructions = value;
                    OnSpecialInstructionsChanged(value);
                    OnSpecialInstructionsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SpecialInstructions);
                }
            }
        }

        /// <inheritdoc cref="errorMessage"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ErrorMessage
        {
            get => errorMessage;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("errorMessage")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(errorMessage, value))
                {
                    string? __oldValue = errorMessage;
                    OnErrorMessageChanging(value);
                    OnErrorMessageChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ErrorMessage);
                    errorMessage = value;
                    OnErrorMessageChanged(value);
                    OnErrorMessageChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ErrorMessage);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="UserPets"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserPets"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserPetsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Pet> value);
        /// <summary>Executes the logic for when <see cref="UserPets"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserPets"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserPetsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Pet>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Pet> newValue);
        /// <summary>Executes the logic for when <see cref="UserPets"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserPets"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserPetsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Pet> value);
        /// <summary>Executes the logic for when <see cref="UserPets"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserPets"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnUserPetsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Pet>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Pet> newValue);
        /// <summary>Executes the logic for when <see cref="SelectedPet"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedPet"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedPetChanging(global::PetSitterConnect.Models.Pet? value);
        /// <summary>Executes the logic for when <see cref="SelectedPet"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedPet"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedPetChanging(global::PetSitterConnect.Models.Pet? oldValue, global::PetSitterConnect.Models.Pet? newValue);
        /// <summary>Executes the logic for when <see cref="SelectedPet"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedPet"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedPetChanged(global::PetSitterConnect.Models.Pet? value);
        /// <summary>Executes the logic for when <see cref="SelectedPet"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedPet"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSelectedPetChanged(global::PetSitterConnect.Models.Pet? oldValue, global::PetSitterConnect.Models.Pet? newValue);
        /// <summary>Executes the logic for when <see cref="Title"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Title"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnTitleChanging(string value);
        /// <summary>Executes the logic for when <see cref="Title"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Title"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnTitleChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Title"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Title"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnTitleChanged(string value);
        /// <summary>Executes the logic for when <see cref="Title"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Title"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnTitleChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Description"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Description"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnDescriptionChanging(string value);
        /// <summary>Executes the logic for when <see cref="Description"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Description"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnDescriptionChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Description"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Description"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnDescriptionChanged(string value);
        /// <summary>Executes the logic for when <see cref="Description"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Description"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnDescriptionChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="StartDate"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="StartDate"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnStartDateChanging(global::System.DateTime value);
        /// <summary>Executes the logic for when <see cref="StartDate"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="StartDate"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnStartDateChanging(global::System.DateTime oldValue, global::System.DateTime newValue);
        /// <summary>Executes the logic for when <see cref="StartDate"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="StartDate"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnStartDateChanged(global::System.DateTime value);
        /// <summary>Executes the logic for when <see cref="StartDate"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="StartDate"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnStartDateChanged(global::System.DateTime oldValue, global::System.DateTime newValue);
        /// <summary>Executes the logic for when <see cref="EndDate"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="EndDate"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnEndDateChanging(global::System.DateTime value);
        /// <summary>Executes the logic for when <see cref="EndDate"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="EndDate"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnEndDateChanging(global::System.DateTime oldValue, global::System.DateTime newValue);
        /// <summary>Executes the logic for when <see cref="EndDate"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="EndDate"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnEndDateChanged(global::System.DateTime value);
        /// <summary>Executes the logic for when <see cref="EndDate"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="EndDate"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnEndDateChanged(global::System.DateTime oldValue, global::System.DateTime newValue);
        /// <summary>Executes the logic for when <see cref="CareType"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CareType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCareTypeChanging(global::PetSitterConnect.Models.CareType value);
        /// <summary>Executes the logic for when <see cref="CareType"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CareType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCareTypeChanging(global::PetSitterConnect.Models.CareType oldValue, global::PetSitterConnect.Models.CareType newValue);
        /// <summary>Executes the logic for when <see cref="CareType"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CareType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCareTypeChanged(global::PetSitterConnect.Models.CareType value);
        /// <summary>Executes the logic for when <see cref="CareType"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CareType"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnCareTypeChanged(global::PetSitterConnect.Models.CareType oldValue, global::PetSitterConnect.Models.CareType newValue);
        /// <summary>Executes the logic for when <see cref="Budget"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Budget"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBudgetChanging(decimal value);
        /// <summary>Executes the logic for when <see cref="Budget"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Budget"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBudgetChanging(decimal oldValue, decimal newValue);
        /// <summary>Executes the logic for when <see cref="Budget"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Budget"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBudgetChanged(decimal value);
        /// <summary>Executes the logic for when <see cref="Budget"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Budget"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBudgetChanged(decimal oldValue, decimal newValue);
        /// <summary>Executes the logic for when <see cref="Location"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Location"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnLocationChanging(string value);
        /// <summary>Executes the logic for when <see cref="Location"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Location"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnLocationChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Location"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Location"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnLocationChanged(string value);
        /// <summary>Executes the logic for when <see cref="Location"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Location"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnLocationChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="SpecialInstructions"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SpecialInstructions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSpecialInstructionsChanging(string value);
        /// <summary>Executes the logic for when <see cref="SpecialInstructions"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SpecialInstructions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSpecialInstructionsChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="SpecialInstructions"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SpecialInstructions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSpecialInstructionsChanged(string value);
        /// <summary>Executes the logic for when <see cref="SpecialInstructions"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SpecialInstructions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSpecialInstructionsChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanging(string value);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanged(string value);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanged(string? oldValue, string newValue);
    }
}