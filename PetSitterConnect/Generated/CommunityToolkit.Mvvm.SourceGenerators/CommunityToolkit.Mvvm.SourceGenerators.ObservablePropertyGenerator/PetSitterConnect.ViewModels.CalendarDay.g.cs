﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class CalendarDay
    {
        /// <inheritdoc cref="date"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.DateTime Date
        {
            get => date;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.DateTime>.Default.Equals(date, value))
                {
                    global::System.DateTime __oldValue = date;
                    OnDateChanging(value);
                    OnDateChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Date);
                    date = value;
                    OnDateChanged(value);
                    OnDateChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Date);
                }
            }
        }

        /// <inheritdoc cref="dayNumber"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public int DayNumber
        {
            get => dayNumber;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<int>.Default.Equals(dayNumber, value))
                {
                    int __oldValue = dayNumber;
                    OnDayNumberChanging(value);
                    OnDayNumberChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.DayNumber);
                    dayNumber = value;
                    OnDayNumberChanged(value);
                    OnDayNumberChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.DayNumber);
                }
            }
        }

        /// <inheritdoc cref="isCurrentMonth"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsCurrentMonth
        {
            get => isCurrentMonth;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isCurrentMonth, value))
                {
                    bool __oldValue = isCurrentMonth;
                    OnIsCurrentMonthChanging(value);
                    OnIsCurrentMonthChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsCurrentMonth);
                    isCurrentMonth = value;
                    OnIsCurrentMonthChanged(value);
                    OnIsCurrentMonthChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsCurrentMonth);
                }
            }
        }

        /// <inheritdoc cref="isToday"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsToday
        {
            get => isToday;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isToday, value))
                {
                    bool __oldValue = isToday;
                    OnIsTodayChanging(value);
                    OnIsTodayChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsToday);
                    isToday = value;
                    OnIsTodayChanged(value);
                    OnIsTodayChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsToday);
                }
            }
        }

        /// <inheritdoc cref="isSelected"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsSelected
        {
            get => isSelected;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isSelected, value))
                {
                    bool __oldValue = isSelected;
                    OnIsSelectedChanging(value);
                    OnIsSelectedChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsSelected);
                    isSelected = value;
                    OnIsSelectedChanged(value);
                    OnIsSelectedChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsSelected);
                }
            }
        }

        /// <inheritdoc cref="isInRange"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsInRange
        {
            get => isInRange;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isInRange, value))
                {
                    bool __oldValue = isInRange;
                    OnIsInRangeChanging(value);
                    OnIsInRangeChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsInRange);
                    isInRange = value;
                    OnIsInRangeChanged(value);
                    OnIsInRangeChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsInRange);
                }
            }
        }

        /// <inheritdoc cref="isAvailable"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsAvailable
        {
            get => isAvailable;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isAvailable, value))
                {
                    bool __oldValue = isAvailable;
                    OnIsAvailableChanging(value);
                    OnIsAvailableChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsAvailable);
                    isAvailable = value;
                    OnIsAvailableChanged(value);
                    OnIsAvailableChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsAvailable);
                }
            }
        }

        /// <inheritdoc cref="hasBookings"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool HasBookings
        {
            get => hasBookings;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(hasBookings, value))
                {
                    bool __oldValue = hasBookings;
                    OnHasBookingsChanging(value);
                    OnHasBookingsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.HasBookings);
                    hasBookings = value;
                    OnHasBookingsChanged(value);
                    OnHasBookingsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.HasBookings);
                }
            }
        }

        /// <inheritdoc cref="bookingCount"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public int BookingCount
        {
            get => bookingCount;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<int>.Default.Equals(bookingCount, value))
                {
                    int __oldValue = bookingCount;
                    OnBookingCountChanging(value);
                    OnBookingCountChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.BookingCount);
                    bookingCount = value;
                    OnBookingCountChanged(value);
                    OnBookingCountChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.BookingCount);
                }
            }
        }

        /// <inheritdoc cref="bookingStatus"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.BookingStatus BookingStatus
        {
            get => bookingStatus;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.BookingStatus>.Default.Equals(bookingStatus, value))
                {
                    global::PetSitterConnect.Models.BookingStatus __oldValue = bookingStatus;
                    OnBookingStatusChanging(value);
                    OnBookingStatusChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.BookingStatus);
                    bookingStatus = value;
                    OnBookingStatusChanged(value);
                    OnBookingStatusChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.BookingStatus);
                }
            }
        }

        /// <inheritdoc cref="isBookingDay"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsBookingDay
        {
            get => isBookingDay;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isBookingDay, value))
                {
                    bool __oldValue = isBookingDay;
                    OnIsBookingDayChanging(value);
                    OnIsBookingDayChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsBookingDay);
                    isBookingDay = value;
                    OnIsBookingDayChanged(value);
                    OnIsBookingDayChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsBookingDay);
                }
            }
        }

        /// <inheritdoc cref="isBookingStart"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsBookingStart
        {
            get => isBookingStart;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isBookingStart, value))
                {
                    bool __oldValue = isBookingStart;
                    OnIsBookingStartChanging(value);
                    OnIsBookingStartChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsBookingStart);
                    isBookingStart = value;
                    OnIsBookingStartChanged(value);
                    OnIsBookingStartChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsBookingStart);
                }
            }
        }

        /// <inheritdoc cref="isBookingEnd"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsBookingEnd
        {
            get => isBookingEnd;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isBookingEnd, value))
                {
                    bool __oldValue = isBookingEnd;
                    OnIsBookingEndChanging(value);
                    OnIsBookingEndChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsBookingEnd);
                    isBookingEnd = value;
                    OnIsBookingEndChanged(value);
                    OnIsBookingEndChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsBookingEnd);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="Date"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Date"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnDateChanging(global::System.DateTime value);
        /// <summary>Executes the logic for when <see cref="Date"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Date"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnDateChanging(global::System.DateTime oldValue, global::System.DateTime newValue);
        /// <summary>Executes the logic for when <see cref="Date"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Date"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnDateChanged(global::System.DateTime value);
        /// <summary>Executes the logic for when <see cref="Date"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Date"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnDateChanged(global::System.DateTime oldValue, global::System.DateTime newValue);
        /// <summary>Executes the logic for when <see cref="DayNumber"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="DayNumber"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnDayNumberChanging(int value);
        /// <summary>Executes the logic for when <see cref="DayNumber"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="DayNumber"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnDayNumberChanging(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="DayNumber"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="DayNumber"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnDayNumberChanged(int value);
        /// <summary>Executes the logic for when <see cref="DayNumber"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="DayNumber"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnDayNumberChanged(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="IsCurrentMonth"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsCurrentMonth"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsCurrentMonthChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsCurrentMonth"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsCurrentMonth"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsCurrentMonthChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsCurrentMonth"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsCurrentMonth"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsCurrentMonthChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsCurrentMonth"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsCurrentMonth"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsCurrentMonthChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsToday"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsToday"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsTodayChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsToday"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsToday"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsTodayChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsToday"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsToday"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsTodayChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsToday"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsToday"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsTodayChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsSelected"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsSelected"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsSelectedChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsSelected"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsSelected"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsSelectedChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsSelected"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsSelected"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsSelectedChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsSelected"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsSelected"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsSelectedChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsInRange"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsInRange"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsInRangeChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsInRange"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsInRange"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsInRangeChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsInRange"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsInRange"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsInRangeChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsInRange"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsInRange"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsInRangeChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsAvailable"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsAvailable"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsAvailableChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsAvailable"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsAvailable"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsAvailableChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsAvailable"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsAvailable"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsAvailableChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsAvailable"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsAvailable"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsAvailableChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="HasBookings"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="HasBookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasBookingsChanging(bool value);
        /// <summary>Executes the logic for when <see cref="HasBookings"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="HasBookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasBookingsChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="HasBookings"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="HasBookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasBookingsChanged(bool value);
        /// <summary>Executes the logic for when <see cref="HasBookings"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="HasBookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasBookingsChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="BookingCount"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="BookingCount"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingCountChanging(int value);
        /// <summary>Executes the logic for when <see cref="BookingCount"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="BookingCount"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingCountChanging(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="BookingCount"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="BookingCount"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingCountChanged(int value);
        /// <summary>Executes the logic for when <see cref="BookingCount"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="BookingCount"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingCountChanged(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="BookingStatus"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="BookingStatus"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingStatusChanging(global::PetSitterConnect.Models.BookingStatus value);
        /// <summary>Executes the logic for when <see cref="BookingStatus"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="BookingStatus"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingStatusChanging(global::PetSitterConnect.Models.BookingStatus oldValue, global::PetSitterConnect.Models.BookingStatus newValue);
        /// <summary>Executes the logic for when <see cref="BookingStatus"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="BookingStatus"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingStatusChanged(global::PetSitterConnect.Models.BookingStatus value);
        /// <summary>Executes the logic for when <see cref="BookingStatus"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="BookingStatus"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingStatusChanged(global::PetSitterConnect.Models.BookingStatus oldValue, global::PetSitterConnect.Models.BookingStatus newValue);
        /// <summary>Executes the logic for when <see cref="IsBookingDay"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsBookingDay"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingDayChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsBookingDay"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsBookingDay"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingDayChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsBookingDay"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsBookingDay"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingDayChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsBookingDay"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsBookingDay"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingDayChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsBookingStart"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsBookingStart"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingStartChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsBookingStart"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsBookingStart"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingStartChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsBookingStart"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsBookingStart"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingStartChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsBookingStart"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsBookingStart"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingStartChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsBookingEnd"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsBookingEnd"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingEndChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsBookingEnd"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsBookingEnd"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingEndChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsBookingEnd"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsBookingEnd"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingEndChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsBookingEnd"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsBookingEnd"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsBookingEndChanged(bool oldValue, bool newValue);
    }
}