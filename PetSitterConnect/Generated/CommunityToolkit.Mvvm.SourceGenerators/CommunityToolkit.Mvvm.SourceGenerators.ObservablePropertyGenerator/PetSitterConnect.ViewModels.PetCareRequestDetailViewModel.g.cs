﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class PetCareRequestDetailViewModel
    {
        /// <inheritdoc cref="requestId"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public int RequestId
        {
            get => requestId;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<int>.Default.Equals(requestId, value))
                {
                    int __oldValue = requestId;
                    OnRequestIdChanging(value);
                    OnRequestIdChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.RequestId);
                    requestId = value;
                    OnRequestIdChanged(value);
                    OnRequestIdChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.RequestId);
                }
            }
        }

        /// <inheritdoc cref="petCareRequest"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.PetCareRequest? PetCareRequest
        {
            get => petCareRequest;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.PetCareRequest?>.Default.Equals(petCareRequest, value))
                {
                    global::PetSitterConnect.Models.PetCareRequest? __oldValue = petCareRequest;
                    OnPetCareRequestChanging(value);
                    OnPetCareRequestChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PetCareRequest);
                    petCareRequest = value;
                    OnPetCareRequestChanged(value);
                    OnPetCareRequestChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PetCareRequest);
                }
            }
        }

        /// <inheritdoc cref="currentUser"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.User? CurrentUser
        {
            get => currentUser;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.User?>.Default.Equals(currentUser, value))
                {
                    global::PetSitterConnect.Models.User? __oldValue = currentUser;
                    OnCurrentUserChanging(value);
                    OnCurrentUserChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CurrentUser);
                    currentUser = value;
                    OnCurrentUserChanged(value);
                    OnCurrentUserChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CurrentUser);
                }
            }
        }

        /// <inheritdoc cref="canApply"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool CanApply
        {
            get => canApply;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(canApply, value))
                {
                    bool __oldValue = canApply;
                    OnCanApplyChanging(value);
                    OnCanApplyChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CanApply);
                    canApply = value;
                    OnCanApplyChanged(value);
                    OnCanApplyChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CanApply);
                }
            }
        }

        /// <inheritdoc cref="isOwner"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsOwner
        {
            get => isOwner;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isOwner, value))
                {
                    bool __oldValue = isOwner;
                    OnIsOwnerChanging(value);
                    OnIsOwnerChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsOwner);
                    isOwner = value;
                    OnIsOwnerChanged(value);
                    OnIsOwnerChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsOwner);
                }
            }
        }

        /// <inheritdoc cref="hasApplied"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool HasApplied
        {
            get => hasApplied;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(hasApplied, value))
                {
                    bool __oldValue = hasApplied;
                    OnHasAppliedChanging(value);
                    OnHasAppliedChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.HasApplied);
                    hasApplied = value;
                    OnHasAppliedChanged(value);
                    OnHasAppliedChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.HasApplied);
                }
            }
        }

        /// <inheritdoc cref="existingBooking"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.Booking? ExistingBooking
        {
            get => existingBooking;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.Booking?>.Default.Equals(existingBooking, value))
                {
                    global::PetSitterConnect.Models.Booking? __oldValue = existingBooking;
                    OnExistingBookingChanging(value);
                    OnExistingBookingChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ExistingBooking);
                    existingBooking = value;
                    OnExistingBookingChanged(value);
                    OnExistingBookingChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ExistingBooking);
                }
            }
        }

        /// <inheritdoc cref="applicationStatus"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ApplicationStatus
        {
            get => applicationStatus;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("applicationStatus")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(applicationStatus, value))
                {
                    string? __oldValue = applicationStatus;
                    OnApplicationStatusChanging(value);
                    OnApplicationStatusChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ApplicationStatus);
                    applicationStatus = value;
                    OnApplicationStatusChanged(value);
                    OnApplicationStatusChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ApplicationStatus);
                }
            }
        }

        /// <inheritdoc cref="errorMessage"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ErrorMessage
        {
            get => errorMessage;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("errorMessage")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(errorMessage, value))
                {
                    string? __oldValue = errorMessage;
                    OnErrorMessageChanging(value);
                    OnErrorMessageChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ErrorMessage);
                    errorMessage = value;
                    OnErrorMessageChanged(value);
                    OnErrorMessageChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ErrorMessage);
                }
            }
        }

        /// <inheritdoc cref="applicationNotes"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ApplicationNotes
        {
            get => applicationNotes;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("applicationNotes")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(applicationNotes, value))
                {
                    string? __oldValue = applicationNotes;
                    OnApplicationNotesChanging(value);
                    OnApplicationNotesChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ApplicationNotes);
                    applicationNotes = value;
                    OnApplicationNotesChanged(value);
                    OnApplicationNotesChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ApplicationNotes);
                }
            }
        }

        /// <inheritdoc cref="userRoleIcon"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string UserRoleIcon
        {
            get => userRoleIcon;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleIcon")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(userRoleIcon, value))
                {
                    string? __oldValue = userRoleIcon;
                    OnUserRoleIconChanging(value);
                    OnUserRoleIconChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleIcon);
                    userRoleIcon = value;
                    OnUserRoleIconChanged(value);
                    OnUserRoleIconChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleIcon);
                }
            }
        }

        /// <inheritdoc cref="userRoleLabel"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string UserRoleLabel
        {
            get => userRoleLabel;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleLabel")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(userRoleLabel, value))
                {
                    string? __oldValue = userRoleLabel;
                    OnUserRoleLabelChanging(value);
                    OnUserRoleLabelChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleLabel);
                    userRoleLabel = value;
                    OnUserRoleLabelChanged(value);
                    OnUserRoleLabelChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleLabel);
                }
            }
        }

        /// <inheritdoc cref="userRoleColor"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::Microsoft.Maui.Graphics.Color UserRoleColor
        {
            get => userRoleColor;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleColor")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::Microsoft.Maui.Graphics.Color>.Default.Equals(userRoleColor, value))
                {
                    global::Microsoft.Maui.Graphics.Color? __oldValue = userRoleColor;
                    OnUserRoleColorChanging(value);
                    OnUserRoleColorChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleColor);
                    userRoleColor = value;
                    OnUserRoleColorChanged(value);
                    OnUserRoleColorChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleColor);
                }
            }
        }

        /// <inheritdoc cref="showSitterActions"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool ShowSitterActions
        {
            get => showSitterActions;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(showSitterActions, value))
                {
                    bool __oldValue = showSitterActions;
                    OnShowSitterActionsChanging(value);
                    OnShowSitterActionsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ShowSitterActions);
                    showSitterActions = value;
                    OnShowSitterActionsChanged(value);
                    OnShowSitterActionsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ShowSitterActions);
                }
            }
        }

        /// <inheritdoc cref="actionButtonText"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ActionButtonText
        {
            get => actionButtonText;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("actionButtonText")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(actionButtonText, value))
                {
                    string? __oldValue = actionButtonText;
                    OnActionButtonTextChanging(value);
                    OnActionButtonTextChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ActionButtonText);
                    actionButtonText = value;
                    OnActionButtonTextChanged(value);
                    OnActionButtonTextChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ActionButtonText);
                }
            }
        }

        /// <inheritdoc cref="actionButtonColor"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::Microsoft.Maui.Graphics.Color ActionButtonColor
        {
            get => actionButtonColor;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("actionButtonColor")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::Microsoft.Maui.Graphics.Color>.Default.Equals(actionButtonColor, value))
                {
                    global::Microsoft.Maui.Graphics.Color? __oldValue = actionButtonColor;
                    OnActionButtonColorChanging(value);
                    OnActionButtonColorChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ActionButtonColor);
                    actionButtonColor = value;
                    OnActionButtonColorChanged(value);
                    OnActionButtonColorChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ActionButtonColor);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="RequestId"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="RequestId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRequestIdChanging(int value);
        /// <summary>Executes the logic for when <see cref="RequestId"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="RequestId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRequestIdChanging(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="RequestId"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="RequestId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRequestIdChanged(int value);
        /// <summary>Executes the logic for when <see cref="RequestId"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="RequestId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRequestIdChanged(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="PetCareRequest"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="PetCareRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPetCareRequestChanging(global::PetSitterConnect.Models.PetCareRequest? value);
        /// <summary>Executes the logic for when <see cref="PetCareRequest"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="PetCareRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPetCareRequestChanging(global::PetSitterConnect.Models.PetCareRequest? oldValue, global::PetSitterConnect.Models.PetCareRequest? newValue);
        /// <summary>Executes the logic for when <see cref="PetCareRequest"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="PetCareRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPetCareRequestChanged(global::PetSitterConnect.Models.PetCareRequest? value);
        /// <summary>Executes the logic for when <see cref="PetCareRequest"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="PetCareRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPetCareRequestChanged(global::PetSitterConnect.Models.PetCareRequest? oldValue, global::PetSitterConnect.Models.PetCareRequest? newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="CanApply"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CanApply"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCanApplyChanging(bool value);
        /// <summary>Executes the logic for when <see cref="CanApply"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CanApply"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCanApplyChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="CanApply"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CanApply"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCanApplyChanged(bool value);
        /// <summary>Executes the logic for when <see cref="CanApply"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CanApply"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCanApplyChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsOwner"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsOwner"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsOwnerChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsOwner"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsOwner"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsOwnerChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsOwner"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsOwner"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsOwnerChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsOwner"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsOwner"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnIsOwnerChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="HasApplied"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="HasApplied"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasAppliedChanging(bool value);
        /// <summary>Executes the logic for when <see cref="HasApplied"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="HasApplied"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasAppliedChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="HasApplied"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="HasApplied"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasAppliedChanged(bool value);
        /// <summary>Executes the logic for when <see cref="HasApplied"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="HasApplied"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasAppliedChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="ExistingBooking"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ExistingBooking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnExistingBookingChanging(global::PetSitterConnect.Models.Booking? value);
        /// <summary>Executes the logic for when <see cref="ExistingBooking"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ExistingBooking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnExistingBookingChanging(global::PetSitterConnect.Models.Booking? oldValue, global::PetSitterConnect.Models.Booking? newValue);
        /// <summary>Executes the logic for when <see cref="ExistingBooking"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ExistingBooking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnExistingBookingChanged(global::PetSitterConnect.Models.Booking? value);
        /// <summary>Executes the logic for when <see cref="ExistingBooking"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ExistingBooking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnExistingBookingChanged(global::PetSitterConnect.Models.Booking? oldValue, global::PetSitterConnect.Models.Booking? newValue);
        /// <summary>Executes the logic for when <see cref="ApplicationStatus"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ApplicationStatus"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationStatusChanging(string value);
        /// <summary>Executes the logic for when <see cref="ApplicationStatus"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ApplicationStatus"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationStatusChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ApplicationStatus"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ApplicationStatus"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationStatusChanged(string value);
        /// <summary>Executes the logic for when <see cref="ApplicationStatus"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ApplicationStatus"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationStatusChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnErrorMessageChanging(string value);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnErrorMessageChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnErrorMessageChanged(string value);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnErrorMessageChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ApplicationNotes"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ApplicationNotes"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationNotesChanging(string value);
        /// <summary>Executes the logic for when <see cref="ApplicationNotes"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ApplicationNotes"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationNotesChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ApplicationNotes"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ApplicationNotes"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationNotesChanged(string value);
        /// <summary>Executes the logic for when <see cref="ApplicationNotes"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ApplicationNotes"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationNotesChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanging(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanged(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanging(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanged(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanging(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanging(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanged(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanged(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
        /// <summary>Executes the logic for when <see cref="ShowSitterActions"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ShowSitterActions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnShowSitterActionsChanging(bool value);
        /// <summary>Executes the logic for when <see cref="ShowSitterActions"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ShowSitterActions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnShowSitterActionsChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="ShowSitterActions"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ShowSitterActions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnShowSitterActionsChanged(bool value);
        /// <summary>Executes the logic for when <see cref="ShowSitterActions"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ShowSitterActions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnShowSitterActionsChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="ActionButtonText"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ActionButtonText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnActionButtonTextChanging(string value);
        /// <summary>Executes the logic for when <see cref="ActionButtonText"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ActionButtonText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnActionButtonTextChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ActionButtonText"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ActionButtonText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnActionButtonTextChanged(string value);
        /// <summary>Executes the logic for when <see cref="ActionButtonText"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ActionButtonText"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnActionButtonTextChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ActionButtonColor"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ActionButtonColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnActionButtonColorChanging(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="ActionButtonColor"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ActionButtonColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnActionButtonColorChanging(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
        /// <summary>Executes the logic for when <see cref="ActionButtonColor"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ActionButtonColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnActionButtonColorChanged(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="ActionButtonColor"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ActionButtonColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnActionButtonColorChanged(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
    }
}