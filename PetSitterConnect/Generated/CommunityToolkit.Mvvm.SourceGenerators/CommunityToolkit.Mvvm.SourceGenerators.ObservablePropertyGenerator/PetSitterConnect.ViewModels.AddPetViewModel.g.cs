﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class AddPetViewModel
    {
        /// <inheritdoc cref="name"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Name
        {
            get => name;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("name")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(name, value))
                {
                    string? __oldValue = name;
                    OnNameChanging(value);
                    OnNameChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Name);
                    name = value;
                    OnNameChanged(value);
                    OnNameChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Name);
                }
            }
        }

        /// <inheritdoc cref="@type"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.PetType Type
        {
            get => @type;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.PetType>.Default.Equals(@type, value))
                {
                    global::PetSitterConnect.Models.PetType __oldValue = @type;
                    OnTypeChanging(value);
                    OnTypeChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Type);
                    @type = value;
                    OnTypeChanged(value);
                    OnTypeChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Type);
                }
            }
        }

        /// <inheritdoc cref="breed"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Breed
        {
            get => breed;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("breed")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(breed, value))
                {
                    string? __oldValue = breed;
                    OnBreedChanging(value);
                    OnBreedChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Breed);
                    breed = value;
                    OnBreedChanged(value);
                    OnBreedChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Breed);
                }
            }
        }

        /// <inheritdoc cref="age"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public int Age
        {
            get => age;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<int>.Default.Equals(age, value))
                {
                    int __oldValue = age;
                    OnAgeChanging(value);
                    OnAgeChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Age);
                    age = value;
                    OnAgeChanged(value);
                    OnAgeChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Age);
                }
            }
        }

        /// <inheritdoc cref="gender"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Gender
        {
            get => gender;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("gender")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(gender, value))
                {
                    string? __oldValue = gender;
                    OnGenderChanging(value);
                    OnGenderChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Gender);
                    gender = value;
                    OnGenderChanged(value);
                    OnGenderChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Gender);
                }
            }
        }

        /// <inheritdoc cref="weight"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public double Weight
        {
            get => weight;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<double>.Default.Equals(weight, value))
                {
                    double __oldValue = weight;
                    OnWeightChanging(value);
                    OnWeightChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Weight);
                    weight = value;
                    OnWeightChanged(value);
                    OnWeightChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Weight);
                }
            }
        }

        /// <inheritdoc cref="size"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Size
        {
            get => size;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("size")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(size, value))
                {
                    string? __oldValue = size;
                    OnSizeChanging(value);
                    OnSizeChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Size);
                    size = value;
                    OnSizeChanged(value);
                    OnSizeChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Size);
                }
            }
        }

        /// <inheritdoc cref="description"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Description
        {
            get => description;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("description")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(description, value))
                {
                    string? __oldValue = description;
                    OnDescriptionChanging(value);
                    OnDescriptionChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Description);
                    description = value;
                    OnDescriptionChanged(value);
                    OnDescriptionChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Description);
                }
            }
        }

        /// <inheritdoc cref="specialNeeds"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string SpecialNeeds
        {
            get => specialNeeds;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("specialNeeds")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(specialNeeds, value))
                {
                    string? __oldValue = specialNeeds;
                    OnSpecialNeedsChanging(value);
                    OnSpecialNeedsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SpecialNeeds);
                    specialNeeds = value;
                    OnSpecialNeedsChanged(value);
                    OnSpecialNeedsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SpecialNeeds);
                }
            }
        }

        /// <inheritdoc cref="medicalConditions"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string MedicalConditions
        {
            get => medicalConditions;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("medicalConditions")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(medicalConditions, value))
                {
                    string? __oldValue = medicalConditions;
                    OnMedicalConditionsChanging(value);
                    OnMedicalConditionsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.MedicalConditions);
                    medicalConditions = value;
                    OnMedicalConditionsChanged(value);
                    OnMedicalConditionsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.MedicalConditions);
                }
            }
        }

        /// <inheritdoc cref="medications"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Medications
        {
            get => medications;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("medications")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(medications, value))
                {
                    string? __oldValue = medications;
                    OnMedicationsChanging(value);
                    OnMedicationsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Medications);
                    medications = value;
                    OnMedicationsChanged(value);
                    OnMedicationsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Medications);
                }
            }
        }

        /// <inheritdoc cref="feedingInstructions"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string FeedingInstructions
        {
            get => feedingInstructions;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("feedingInstructions")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(feedingInstructions, value))
                {
                    string? __oldValue = feedingInstructions;
                    OnFeedingInstructionsChanging(value);
                    OnFeedingInstructionsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FeedingInstructions);
                    feedingInstructions = value;
                    OnFeedingInstructionsChanged(value);
                    OnFeedingInstructionsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FeedingInstructions);
                }
            }
        }

        /// <inheritdoc cref="isVaccinated"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsVaccinated
        {
            get => isVaccinated;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isVaccinated, value))
                {
                    bool __oldValue = isVaccinated;
                    OnIsVaccinatedChanging(value);
                    OnIsVaccinatedChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsVaccinated);
                    isVaccinated = value;
                    OnIsVaccinatedChanged(value);
                    OnIsVaccinatedChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsVaccinated);
                }
            }
        }

        /// <inheritdoc cref="isNeutered"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsNeutered
        {
            get => isNeutered;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isNeutered, value))
                {
                    bool __oldValue = isNeutered;
                    OnIsNeuteredChanging(value);
                    OnIsNeuteredChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsNeutered);
                    isNeutered = value;
                    OnIsNeuteredChanged(value);
                    OnIsNeuteredChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsNeutered);
                }
            }
        }

        /// <inheritdoc cref="isFriendlyWithOtherPets"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsFriendlyWithOtherPets
        {
            get => isFriendlyWithOtherPets;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isFriendlyWithOtherPets, value))
                {
                    bool __oldValue = isFriendlyWithOtherPets;
                    OnIsFriendlyWithOtherPetsChanging(value);
                    OnIsFriendlyWithOtherPetsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsFriendlyWithOtherPets);
                    isFriendlyWithOtherPets = value;
                    OnIsFriendlyWithOtherPetsChanged(value);
                    OnIsFriendlyWithOtherPetsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsFriendlyWithOtherPets);
                }
            }
        }

        /// <inheritdoc cref="isFriendlyWithChildren"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool IsFriendlyWithChildren
        {
            get => isFriendlyWithChildren;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(isFriendlyWithChildren, value))
                {
                    bool __oldValue = isFriendlyWithChildren;
                    OnIsFriendlyWithChildrenChanging(value);
                    OnIsFriendlyWithChildrenChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.IsFriendlyWithChildren);
                    isFriendlyWithChildren = value;
                    OnIsFriendlyWithChildrenChanged(value);
                    OnIsFriendlyWithChildrenChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.IsFriendlyWithChildren);
                }
            }
        }

        /// <inheritdoc cref="errorMessage"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ErrorMessage
        {
            get => errorMessage;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("errorMessage")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(errorMessage, value))
                {
                    string? __oldValue = errorMessage;
                    OnErrorMessageChanging(value);
                    OnErrorMessageChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ErrorMessage);
                    errorMessage = value;
                    OnErrorMessageChanged(value);
                    OnErrorMessageChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ErrorMessage);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="Name"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Name"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnNameChanging(string value);
        /// <summary>Executes the logic for when <see cref="Name"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Name"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnNameChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Name"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Name"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnNameChanged(string value);
        /// <summary>Executes the logic for when <see cref="Name"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Name"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnNameChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Type"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Type"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnTypeChanging(global::PetSitterConnect.Models.PetType value);
        /// <summary>Executes the logic for when <see cref="Type"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Type"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnTypeChanging(global::PetSitterConnect.Models.PetType oldValue, global::PetSitterConnect.Models.PetType newValue);
        /// <summary>Executes the logic for when <see cref="Type"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Type"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnTypeChanged(global::PetSitterConnect.Models.PetType value);
        /// <summary>Executes the logic for when <see cref="Type"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Type"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnTypeChanged(global::PetSitterConnect.Models.PetType oldValue, global::PetSitterConnect.Models.PetType newValue);
        /// <summary>Executes the logic for when <see cref="Breed"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Breed"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBreedChanging(string value);
        /// <summary>Executes the logic for when <see cref="Breed"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Breed"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBreedChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Breed"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Breed"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBreedChanged(string value);
        /// <summary>Executes the logic for when <see cref="Breed"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Breed"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnBreedChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Age"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Age"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAgeChanging(int value);
        /// <summary>Executes the logic for when <see cref="Age"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Age"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAgeChanging(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="Age"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Age"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAgeChanged(int value);
        /// <summary>Executes the logic for when <see cref="Age"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Age"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnAgeChanged(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="Gender"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Gender"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnGenderChanging(string value);
        /// <summary>Executes the logic for when <see cref="Gender"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Gender"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnGenderChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Gender"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Gender"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnGenderChanged(string value);
        /// <summary>Executes the logic for when <see cref="Gender"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Gender"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnGenderChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Weight"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Weight"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnWeightChanging(double value);
        /// <summary>Executes the logic for when <see cref="Weight"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Weight"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnWeightChanging(double oldValue, double newValue);
        /// <summary>Executes the logic for when <see cref="Weight"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Weight"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnWeightChanged(double value);
        /// <summary>Executes the logic for when <see cref="Weight"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Weight"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnWeightChanged(double oldValue, double newValue);
        /// <summary>Executes the logic for when <see cref="Size"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Size"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSizeChanging(string value);
        /// <summary>Executes the logic for when <see cref="Size"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Size"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSizeChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Size"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Size"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSizeChanged(string value);
        /// <summary>Executes the logic for when <see cref="Size"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Size"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSizeChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Description"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Description"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnDescriptionChanging(string value);
        /// <summary>Executes the logic for when <see cref="Description"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Description"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnDescriptionChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Description"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Description"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnDescriptionChanged(string value);
        /// <summary>Executes the logic for when <see cref="Description"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Description"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnDescriptionChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="SpecialNeeds"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SpecialNeeds"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSpecialNeedsChanging(string value);
        /// <summary>Executes the logic for when <see cref="SpecialNeeds"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SpecialNeeds"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSpecialNeedsChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="SpecialNeeds"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SpecialNeeds"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSpecialNeedsChanged(string value);
        /// <summary>Executes the logic for when <see cref="SpecialNeeds"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SpecialNeeds"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnSpecialNeedsChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="MedicalConditions"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="MedicalConditions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMedicalConditionsChanging(string value);
        /// <summary>Executes the logic for when <see cref="MedicalConditions"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="MedicalConditions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMedicalConditionsChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="MedicalConditions"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="MedicalConditions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMedicalConditionsChanged(string value);
        /// <summary>Executes the logic for when <see cref="MedicalConditions"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="MedicalConditions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMedicalConditionsChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Medications"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Medications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMedicationsChanging(string value);
        /// <summary>Executes the logic for when <see cref="Medications"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Medications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMedicationsChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Medications"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Medications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMedicationsChanged(string value);
        /// <summary>Executes the logic for when <see cref="Medications"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Medications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnMedicationsChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="FeedingInstructions"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="FeedingInstructions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFeedingInstructionsChanging(string value);
        /// <summary>Executes the logic for when <see cref="FeedingInstructions"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="FeedingInstructions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFeedingInstructionsChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="FeedingInstructions"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="FeedingInstructions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFeedingInstructionsChanged(string value);
        /// <summary>Executes the logic for when <see cref="FeedingInstructions"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="FeedingInstructions"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnFeedingInstructionsChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="IsVaccinated"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsVaccinated"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsVaccinatedChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsVaccinated"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsVaccinated"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsVaccinatedChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsVaccinated"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsVaccinated"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsVaccinatedChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsVaccinated"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsVaccinated"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsVaccinatedChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsNeutered"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsNeutered"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsNeuteredChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsNeutered"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsNeutered"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsNeuteredChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsNeutered"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsNeutered"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsNeuteredChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsNeutered"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsNeutered"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsNeuteredChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsFriendlyWithOtherPets"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsFriendlyWithOtherPets"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsFriendlyWithOtherPetsChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsFriendlyWithOtherPets"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsFriendlyWithOtherPets"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsFriendlyWithOtherPetsChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsFriendlyWithOtherPets"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsFriendlyWithOtherPets"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsFriendlyWithOtherPetsChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsFriendlyWithOtherPets"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsFriendlyWithOtherPets"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsFriendlyWithOtherPetsChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsFriendlyWithChildren"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsFriendlyWithChildren"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsFriendlyWithChildrenChanging(bool value);
        /// <summary>Executes the logic for when <see cref="IsFriendlyWithChildren"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="IsFriendlyWithChildren"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsFriendlyWithChildrenChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="IsFriendlyWithChildren"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsFriendlyWithChildren"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsFriendlyWithChildrenChanged(bool value);
        /// <summary>Executes the logic for when <see cref="IsFriendlyWithChildren"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="IsFriendlyWithChildren"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnIsFriendlyWithChildrenChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanging(string value);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanged(string value);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "*******")]
        partial void OnErrorMessageChanged(string? oldValue, string newValue);
    }
}