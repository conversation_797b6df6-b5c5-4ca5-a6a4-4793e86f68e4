﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class BookingListViewModel
    {
        /// <inheritdoc cref="bookings"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> Bookings
        {
            get => bookings;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("bookings")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>>.Default.Equals(bookings, value))
                {
                    global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>? __oldValue = bookings;
                    OnBookingsChanging(value);
                    OnBookingsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Bookings);
                    bookings = value;
                    OnBookingsChanged(value);
                    OnBookingsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Bookings);
                }
            }
        }

        /// <inheritdoc cref="filteredBookings"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> FilteredBookings
        {
            get => filteredBookings;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("filteredBookings")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>>.Default.Equals(filteredBookings, value))
                {
                    global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>? __oldValue = filteredBookings;
                    OnFilteredBookingsChanging(value);
                    OnFilteredBookingsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.FilteredBookings);
                    filteredBookings = value;
                    OnFilteredBookingsChanged(value);
                    OnFilteredBookingsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.FilteredBookings);
                }
            }
        }

        /// <inheritdoc cref="selectedFilter"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.ViewModels.BookingFilter SelectedFilter
        {
            get => selectedFilter;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.ViewModels.BookingFilter>.Default.Equals(selectedFilter, value))
                {
                    global::PetSitterConnect.ViewModels.BookingFilter __oldValue = selectedFilter;
                    OnSelectedFilterChanging(value);
                    OnSelectedFilterChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedFilter);
                    selectedFilter = value;
                    OnSelectedFilterChanged(value);
                    OnSelectedFilterChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedFilter);
                }
            }
        }

        /// <inheritdoc cref="currentUser"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.User? CurrentUser
        {
            get => currentUser;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.User?>.Default.Equals(currentUser, value))
                {
                    global::PetSitterConnect.Models.User? __oldValue = currentUser;
                    OnCurrentUserChanging(value);
                    OnCurrentUserChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CurrentUser);
                    currentUser = value;
                    OnCurrentUserChanged(value);
                    OnCurrentUserChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CurrentUser);
                }
            }
        }

        /// <inheritdoc cref="showAsSitter"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool ShowAsSitter
        {
            get => showAsSitter;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(showAsSitter, value))
                {
                    bool __oldValue = showAsSitter;
                    OnShowAsSitterChanging(value);
                    OnShowAsSitterChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ShowAsSitter);
                    showAsSitter = value;
                    OnShowAsSitterChanged(value);
                    OnShowAsSitterChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ShowAsSitter);
                }
            }
        }

        /// <inheritdoc cref="selectedBooking"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.Booking? SelectedBooking
        {
            get => selectedBooking;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.Booking?>.Default.Equals(selectedBooking, value))
                {
                    global::PetSitterConnect.Models.Booking? __oldValue = selectedBooking;
                    OnSelectedBookingChanging(value);
                    OnSelectedBookingChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedBooking);
                    selectedBooking = value;
                    OnSelectedBookingChanged(value);
                    OnSelectedBookingChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedBooking);
                }
            }
        }

        /// <inheritdoc cref="userRoleIcon"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string UserRoleIcon
        {
            get => userRoleIcon;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleIcon")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(userRoleIcon, value))
                {
                    string? __oldValue = userRoleIcon;
                    OnUserRoleIconChanging(value);
                    OnUserRoleIconChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleIcon);
                    userRoleIcon = value;
                    OnUserRoleIconChanged(value);
                    OnUserRoleIconChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleIcon);
                }
            }
        }

        /// <inheritdoc cref="userRoleLabel"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string UserRoleLabel
        {
            get => userRoleLabel;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleLabel")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(userRoleLabel, value))
                {
                    string? __oldValue = userRoleLabel;
                    OnUserRoleLabelChanging(value);
                    OnUserRoleLabelChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleLabel);
                    userRoleLabel = value;
                    OnUserRoleLabelChanged(value);
                    OnUserRoleLabelChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleLabel);
                }
            }
        }

        /// <inheritdoc cref="userRoleColor"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::Microsoft.Maui.Graphics.Color UserRoleColor
        {
            get => userRoleColor;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("userRoleColor")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::Microsoft.Maui.Graphics.Color>.Default.Equals(userRoleColor, value))
                {
                    global::Microsoft.Maui.Graphics.Color? __oldValue = userRoleColor;
                    OnUserRoleColorChanging(value);
                    OnUserRoleColorChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.UserRoleColor);
                    userRoleColor = value;
                    OnUserRoleColorChanged(value);
                    OnUserRoleColorChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.UserRoleColor);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="Bookings"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Bookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> value);
        /// <summary>Executes the logic for when <see cref="Bookings"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Bookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> newValue);
        /// <summary>Executes the logic for when <see cref="Bookings"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Bookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> value);
        /// <summary>Executes the logic for when <see cref="Bookings"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Bookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnBookingsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> newValue);
        /// <summary>Executes the logic for when <see cref="FilteredBookings"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="FilteredBookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnFilteredBookingsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> value);
        /// <summary>Executes the logic for when <see cref="FilteredBookings"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="FilteredBookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnFilteredBookingsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> newValue);
        /// <summary>Executes the logic for when <see cref="FilteredBookings"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="FilteredBookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnFilteredBookingsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> value);
        /// <summary>Executes the logic for when <see cref="FilteredBookings"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="FilteredBookings"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnFilteredBookingsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> newValue);
        /// <summary>Executes the logic for when <see cref="SelectedFilter"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedFilter"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedFilterChanging(global::PetSitterConnect.ViewModels.BookingFilter value);
        /// <summary>Executes the logic for when <see cref="SelectedFilter"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedFilter"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedFilterChanging(global::PetSitterConnect.ViewModels.BookingFilter oldValue, global::PetSitterConnect.ViewModels.BookingFilter newValue);
        /// <summary>Executes the logic for when <see cref="SelectedFilter"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedFilter"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedFilterChanged(global::PetSitterConnect.ViewModels.BookingFilter value);
        /// <summary>Executes the logic for when <see cref="SelectedFilter"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedFilter"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedFilterChanged(global::PetSitterConnect.ViewModels.BookingFilter oldValue, global::PetSitterConnect.ViewModels.BookingFilter newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="ShowAsSitter"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ShowAsSitter"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnShowAsSitterChanging(bool value);
        /// <summary>Executes the logic for when <see cref="ShowAsSitter"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ShowAsSitter"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnShowAsSitterChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="ShowAsSitter"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ShowAsSitter"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnShowAsSitterChanged(bool value);
        /// <summary>Executes the logic for when <see cref="ShowAsSitter"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ShowAsSitter"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnShowAsSitterChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="SelectedBooking"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedBooking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedBookingChanging(global::PetSitterConnect.Models.Booking? value);
        /// <summary>Executes the logic for when <see cref="SelectedBooking"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedBooking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedBookingChanging(global::PetSitterConnect.Models.Booking? oldValue, global::PetSitterConnect.Models.Booking? newValue);
        /// <summary>Executes the logic for when <see cref="SelectedBooking"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedBooking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedBookingChanged(global::PetSitterConnect.Models.Booking? value);
        /// <summary>Executes the logic for when <see cref="SelectedBooking"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedBooking"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedBookingChanged(global::PetSitterConnect.Models.Booking? oldValue, global::PetSitterConnect.Models.Booking? newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanging(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanged(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleIcon"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleIcon"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleIconChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanging(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanged(string value);
        /// <summary>Executes the logic for when <see cref="UserRoleLabel"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleLabel"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleLabelChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanging(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanging(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanged(global::Microsoft.Maui.Graphics.Color value);
        /// <summary>Executes the logic for when <see cref="UserRoleColor"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="UserRoleColor"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnUserRoleColorChanged(global::Microsoft.Maui.Graphics.Color? oldValue, global::Microsoft.Maui.Graphics.Color newValue);
    }
}