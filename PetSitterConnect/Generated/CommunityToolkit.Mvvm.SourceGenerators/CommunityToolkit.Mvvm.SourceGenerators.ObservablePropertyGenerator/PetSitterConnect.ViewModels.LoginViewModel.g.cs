﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class LoginViewModel
    {
        /// <inheritdoc cref="email"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Email
        {
            get => email;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("email")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(email, value))
                {
                    string? __oldValue = email;
                    OnEmailChanging(value);
                    OnEmailChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Email);
                    email = value;
                    OnEmailChanged(value);
                    OnEmailChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Email);
                }
            }
        }

        /// <inheritdoc cref="password"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string Password
        {
            get => password;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("password")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(password, value))
                {
                    string? __oldValue = password;
                    OnPasswordChanging(value);
                    OnPasswordChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Password);
                    password = value;
                    OnPasswordChanged(value);
                    OnPasswordChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Password);
                }
            }
        }

        /// <inheritdoc cref="rememberMe"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool RememberMe
        {
            get => rememberMe;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(rememberMe, value))
                {
                    bool __oldValue = rememberMe;
                    OnRememberMeChanging(value);
                    OnRememberMeChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.RememberMe);
                    rememberMe = value;
                    OnRememberMeChanged(value);
                    OnRememberMeChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.RememberMe);
                }
            }
        }

        /// <inheritdoc cref="errorMessage"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public string ErrorMessage
        {
            get => errorMessage;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("errorMessage")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<string>.Default.Equals(errorMessage, value))
                {
                    string? __oldValue = errorMessage;
                    OnErrorMessageChanging(value);
                    OnErrorMessageChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.ErrorMessage);
                    errorMessage = value;
                    OnErrorMessageChanged(value);
                    OnErrorMessageChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.ErrorMessage);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="Email"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Email"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnEmailChanging(string value);
        /// <summary>Executes the logic for when <see cref="Email"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Email"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnEmailChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Email"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Email"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnEmailChanged(string value);
        /// <summary>Executes the logic for when <see cref="Email"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Email"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnEmailChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Password"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Password"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPasswordChanging(string value);
        /// <summary>Executes the logic for when <see cref="Password"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Password"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPasswordChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="Password"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Password"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPasswordChanged(string value);
        /// <summary>Executes the logic for when <see cref="Password"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Password"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPasswordChanged(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="RememberMe"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="RememberMe"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRememberMeChanging(bool value);
        /// <summary>Executes the logic for when <see cref="RememberMe"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="RememberMe"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRememberMeChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="RememberMe"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="RememberMe"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRememberMeChanged(bool value);
        /// <summary>Executes the logic for when <see cref="RememberMe"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="RememberMe"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRememberMeChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnErrorMessageChanging(string value);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnErrorMessageChanging(string? oldValue, string newValue);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnErrorMessageChanged(string value);
        /// <summary>Executes the logic for when <see cref="ErrorMessage"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="ErrorMessage"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnErrorMessageChanged(string? oldValue, string newValue);
    }
}