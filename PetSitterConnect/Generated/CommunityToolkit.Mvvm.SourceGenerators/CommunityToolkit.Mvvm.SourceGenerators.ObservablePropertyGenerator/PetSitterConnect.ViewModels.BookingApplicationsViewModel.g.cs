﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class BookingApplicationsViewModel
    {
        /// <inheritdoc cref="requestId"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public int RequestId
        {
            get => requestId;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<int>.Default.Equals(requestId, value))
                {
                    int __oldValue = requestId;
                    OnRequestIdChanging(value);
                    OnRequestIdChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.RequestId);
                    requestId = value;
                    OnRequestIdChanged(value);
                    OnRequestIdChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.RequestId);
                }
            }
        }

        /// <inheritdoc cref="petCareRequest"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.PetCareRequest? PetCareRequest
        {
            get => petCareRequest;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.PetCareRequest?>.Default.Equals(petCareRequest, value))
                {
                    global::PetSitterConnect.Models.PetCareRequest? __oldValue = petCareRequest;
                    OnPetCareRequestChanging(value);
                    OnPetCareRequestChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.PetCareRequest);
                    petCareRequest = value;
                    OnPetCareRequestChanged(value);
                    OnPetCareRequestChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.PetCareRequest);
                }
            }
        }

        /// <inheritdoc cref="applications"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> Applications
        {
            get => applications;
            [global::System.Diagnostics.CodeAnalysis.MemberNotNull("applications")]
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>>.Default.Equals(applications, value))
                {
                    global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>? __oldValue = applications;
                    OnApplicationsChanging(value);
                    OnApplicationsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.Applications);
                    applications = value;
                    OnApplicationsChanged(value);
                    OnApplicationsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.Applications);
                }
            }
        }

        /// <inheritdoc cref="currentUser"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.User? CurrentUser
        {
            get => currentUser;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.User?>.Default.Equals(currentUser, value))
                {
                    global::PetSitterConnect.Models.User? __oldValue = currentUser;
                    OnCurrentUserChanging(value);
                    OnCurrentUserChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.CurrentUser);
                    currentUser = value;
                    OnCurrentUserChanged(value);
                    OnCurrentUserChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.CurrentUser);
                }
            }
        }

        /// <inheritdoc cref="hasApplications"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public bool HasApplications
        {
            get => hasApplications;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<bool>.Default.Equals(hasApplications, value))
                {
                    bool __oldValue = hasApplications;
                    OnHasApplicationsChanging(value);
                    OnHasApplicationsChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.HasApplications);
                    hasApplications = value;
                    OnHasApplicationsChanged(value);
                    OnHasApplicationsChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.HasApplications);
                }
            }
        }

        /// <inheritdoc cref="selectedApplication"/>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::PetSitterConnect.Models.Booking? SelectedApplication
        {
            get => selectedApplication;
            set
            {
                if (!global::System.Collections.Generic.EqualityComparer<global::PetSitterConnect.Models.Booking?>.Default.Equals(selectedApplication, value))
                {
                    global::PetSitterConnect.Models.Booking? __oldValue = selectedApplication;
                    OnSelectedApplicationChanging(value);
                    OnSelectedApplicationChanging(__oldValue, value);
                    OnPropertyChanging(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangingArgs.SelectedApplication);
                    selectedApplication = value;
                    OnSelectedApplicationChanged(value);
                    OnSelectedApplicationChanged(__oldValue, value);
                    OnPropertyChanged(global::CommunityToolkit.Mvvm.ComponentModel.__Internals.__KnownINotifyPropertyChangedArgs.SelectedApplication);
                }
            }
        }

        /// <summary>Executes the logic for when <see cref="RequestId"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="RequestId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRequestIdChanging(int value);
        /// <summary>Executes the logic for when <see cref="RequestId"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="RequestId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRequestIdChanging(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="RequestId"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="RequestId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRequestIdChanged(int value);
        /// <summary>Executes the logic for when <see cref="RequestId"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="RequestId"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnRequestIdChanged(int oldValue, int newValue);
        /// <summary>Executes the logic for when <see cref="PetCareRequest"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="PetCareRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPetCareRequestChanging(global::PetSitterConnect.Models.PetCareRequest? value);
        /// <summary>Executes the logic for when <see cref="PetCareRequest"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="PetCareRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPetCareRequestChanging(global::PetSitterConnect.Models.PetCareRequest? oldValue, global::PetSitterConnect.Models.PetCareRequest? newValue);
        /// <summary>Executes the logic for when <see cref="PetCareRequest"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="PetCareRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPetCareRequestChanged(global::PetSitterConnect.Models.PetCareRequest? value);
        /// <summary>Executes the logic for when <see cref="PetCareRequest"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="PetCareRequest"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnPetCareRequestChanged(global::PetSitterConnect.Models.PetCareRequest? oldValue, global::PetSitterConnect.Models.PetCareRequest? newValue);
        /// <summary>Executes the logic for when <see cref="Applications"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Applications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> value);
        /// <summary>Executes the logic for when <see cref="Applications"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="Applications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationsChanging(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> newValue);
        /// <summary>Executes the logic for when <see cref="Applications"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Applications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> value);
        /// <summary>Executes the logic for when <see cref="Applications"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="Applications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnApplicationsChanged(global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking>? oldValue, global::System.Collections.ObjectModel.ObservableCollection<global::PetSitterConnect.Models.Booking> newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanging(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? value);
        /// <summary>Executes the logic for when <see cref="CurrentUser"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="CurrentUser"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnCurrentUserChanged(global::PetSitterConnect.Models.User? oldValue, global::PetSitterConnect.Models.User? newValue);
        /// <summary>Executes the logic for when <see cref="HasApplications"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="HasApplications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasApplicationsChanging(bool value);
        /// <summary>Executes the logic for when <see cref="HasApplications"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="HasApplications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasApplicationsChanging(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="HasApplications"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="HasApplications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasApplicationsChanged(bool value);
        /// <summary>Executes the logic for when <see cref="HasApplications"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="HasApplications"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnHasApplicationsChanged(bool oldValue, bool newValue);
        /// <summary>Executes the logic for when <see cref="SelectedApplication"/> is changing.</summary>
        /// <param name="value">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedApplication"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedApplicationChanging(global::PetSitterConnect.Models.Booking? value);
        /// <summary>Executes the logic for when <see cref="SelectedApplication"/> is changing.</summary>
        /// <param name="oldValue">The previous property value that is being replaced.</param>
        /// <param name="newValue">The new property value being set.</param>
        /// <remarks>This method is invoked right before the value of <see cref="SelectedApplication"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedApplicationChanging(global::PetSitterConnect.Models.Booking? oldValue, global::PetSitterConnect.Models.Booking? newValue);
        /// <summary>Executes the logic for when <see cref="SelectedApplication"/> just changed.</summary>
        /// <param name="value">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedApplication"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedApplicationChanged(global::PetSitterConnect.Models.Booking? value);
        /// <summary>Executes the logic for when <see cref="SelectedApplication"/> just changed.</summary>
        /// <param name="oldValue">The previous property value that was replaced.</param>
        /// <param name="newValue">The new property value that was set.</param>
        /// <remarks>This method is invoked right after the value of <see cref="SelectedApplication"/> is changed.</remarks>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.ObservablePropertyGenerator", "8.4.0.0")]
        partial void OnSelectedApplicationChanged(global::PetSitterConnect.Models.Booking? oldValue, global::PetSitterConnect.Models.Booking? newValue);
    }
}