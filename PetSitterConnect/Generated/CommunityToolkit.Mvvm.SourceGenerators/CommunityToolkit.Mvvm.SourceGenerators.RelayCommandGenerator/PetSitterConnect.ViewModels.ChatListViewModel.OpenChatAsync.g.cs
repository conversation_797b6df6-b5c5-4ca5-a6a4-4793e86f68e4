﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class ChatListViewModel
    {
        /// <summary>The backing field for <see cref="OpenChatCommand"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        private global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand<global::PetSitterConnect.Services.ChatConversation>? openChatCommand;
        /// <summary>Gets an <see cref="global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand{T}"/> instance wrapping <see cref="OpenChatAsync"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand<global::PetSitterConnect.Services.ChatConversation> OpenChatCommand => openChatCommand ??= new global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand<global::PetSitterConnect.Services.ChatConversation>(new global::System.Func<global::PetSitterConnect.Services.ChatConversation, global::System.Threading.Tasks.Task>(OpenChatAsync));
    }
}