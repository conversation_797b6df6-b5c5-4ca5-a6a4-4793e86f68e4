﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class PetCareRequestDetailViewModel
    {
        /// <summary>The backing field for <see cref="ContactOwnerCommand"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        private global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand? contactOwnerCommand;
        /// <summary>Gets an <see cref="global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance wrapping <see cref="ContactOwnerAsync"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand ContactOwnerCommand => contactOwnerCommand ??= new global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand(new global::System.Func<global::System.Threading.Tasks.Task>(ContactOwnerAsync));
    }
}