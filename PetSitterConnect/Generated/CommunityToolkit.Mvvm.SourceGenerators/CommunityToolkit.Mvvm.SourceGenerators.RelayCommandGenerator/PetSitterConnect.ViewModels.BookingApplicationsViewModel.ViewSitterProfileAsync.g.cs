﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class BookingApplicationsViewModel
    {
        /// <summary>The backing field for <see cref="ViewSitterProfileCommand"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        private global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand<global::PetSitterConnect.Models.Booking>? viewSitterProfileCommand;
        /// <summary>Gets an <see cref="global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand{T}"/> instance wrapping <see cref="ViewSitterProfileAsync"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand<global::PetSitterConnect.Models.Booking> ViewSitterProfileCommand => viewSitterProfileCommand ??= new global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand<global::PetSitterConnect.Models.Booking>(new global::System.Func<global::PetSitterConnect.Models.Booking, global::System.Threading.Tasks.Task>(ViewSitterProfileAsync));
    }
}