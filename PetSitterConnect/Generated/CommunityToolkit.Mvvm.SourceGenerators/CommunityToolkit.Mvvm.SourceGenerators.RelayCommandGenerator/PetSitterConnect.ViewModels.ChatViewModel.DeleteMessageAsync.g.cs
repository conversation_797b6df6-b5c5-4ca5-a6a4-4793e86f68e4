﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class ChatViewModel
    {
        /// <summary>The backing field for <see cref="DeleteMessageCommand"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        private global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand<global::PetSitterConnect.Models.ChatMessage>? deleteMessageCommand;
        /// <summary>Gets an <see cref="global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand{T}"/> instance wrapping <see cref="DeleteMessageAsync"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand<global::PetSitterConnect.Models.ChatMessage> DeleteMessageCommand => deleteMessageCommand ??= new global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand<global::PetSitterConnect.Models.ChatMessage>(new global::System.Func<global::PetSitterConnect.Models.ChatMessage, global::System.Threading.Tasks.Task>(DeleteMessageAsync));
    }
}