﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class PetCareRequestListViewModel
    {
        /// <summary>The backing field for <see cref="ViewRequestDetailsCommand"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        private global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand<global::PetSitterConnect.Models.PetCareRequest>? viewRequestDetailsCommand;
        /// <summary>Gets an <see cref="global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand{T}"/> instance wrapping <see cref="ViewRequestDetailsAsync"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand<global::PetSitterConnect.Models.PetCareRequest> ViewRequestDetailsCommand => viewRequestDetailsCommand ??= new global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand<global::PetSitterConnect.Models.PetCareRequest>(new global::System.Func<global::PetSitterConnect.Models.PetCareRequest, global::System.Threading.Tasks.Task>(ViewRequestDetailsAsync));
    }
}