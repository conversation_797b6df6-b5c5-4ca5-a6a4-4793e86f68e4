﻿// <auto-generated/>
#pragma warning disable
#nullable enable
namespace PetSitterConnect.ViewModels
{
    /// <inheritdoc/>
    partial class CalendarBookingViewModel
    {
        /// <summary>The backing field for <see cref="ViewBookingDetailsCommand"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        private global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand<global::PetSitterConnect.Models.Booking>? viewBookingDetailsCommand;
        /// <summary>Gets an <see cref="global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand{T}"/> instance wrapping <see cref="ViewBookingDetailsAsync"/>.</summary>
        [global::System.CodeDom.Compiler.GeneratedCode("CommunityToolkit.Mvvm.SourceGenerators.RelayCommandGenerator", "8.4.0.0")]
        [global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
        public global::CommunityToolkit.Mvvm.Input.IAsyncRelayCommand<global::PetSitterConnect.Models.Booking> ViewBookingDetailsCommand => viewBookingDetailsCommand ??= new global::CommunityToolkit.Mvvm.Input.AsyncRelayCommand<global::PetSitterConnect.Models.Booking>(new global::System.Func<global::PetSitterConnect.Models.Booking, global::System.Threading.Tasks.Task>(ViewBookingDetailsAsync));
    }
}