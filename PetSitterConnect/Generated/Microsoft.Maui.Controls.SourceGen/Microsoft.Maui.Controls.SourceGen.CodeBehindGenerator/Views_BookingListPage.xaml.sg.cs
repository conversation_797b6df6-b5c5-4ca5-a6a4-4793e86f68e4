﻿
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a .NET MAUI source generator.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: global::Microsoft.Maui.Controls.Xaml.XamlResourceId("PetSitterConnect.Views.BookingListPage.xaml", "Views/BookingListPage.xaml", typeof(global::PetSitterConnect.Views.BookingListPage))]
namespace PetSitterConnect.Views
{
	[global::Microsoft.Maui.Controls.Xaml.XamlFilePath("Views/BookingListPage.xaml")]
	public partial class BookingListPage : global::Microsoft.Maui.Controls.ContentPage
	{
		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
#if NET5_0_OR_GREATER
#endif
		private void InitializeComponent()
		{
#pragma warning disable IL2026, IL3050 // The body of InitializeComponent will be replaced by XamlC so LoadFromXaml will never be called in production builds
			global::Microsoft.Maui.Controls.Xaml.Extensions.LoadFromXaml(this, typeof(BookingListPage));
#pragma warning restore IL2026, IL3050
		}
	}
}
