﻿
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a .NET MAUI source generator.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: global::Microsoft.Maui.Controls.Xaml.XamlResourceId("PetSitterConnect.Views.ChatPage.xaml", "Views/ChatPage.xaml", typeof(global::PetSitterConnect.Views.ChatPage))]
namespace PetSitterConnect.Views
{
	[global::Microsoft.Maui.Controls.Xaml.XamlFilePath("Views/ChatPage.xaml")]
	public partial class ChatPage : global::Microsoft.Maui.Controls.ContentPage
	{
		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
		private global::Microsoft.Maui.Controls.CollectionView MessagesCollectionView;

		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
#if NET5_0_OR_GREATER
		[global::System.Diagnostics.CodeAnalysis.MemberNotNullAttribute(nameof(MessagesCollectionView))]
#endif
		private void InitializeComponent()
		{
#pragma warning disable IL2026, IL3050 // The body of InitializeComponent will be replaced by XamlC so LoadFromXaml will never be called in production builds
			global::Microsoft.Maui.Controls.Xaml.Extensions.LoadFromXaml(this, typeof(ChatPage));
			MessagesCollectionView = global::Microsoft.Maui.Controls.NameScopeExtensions.FindByName<global::Microsoft.Maui.Controls.CollectionView>(this, "MessagesCollectionView");
#pragma warning restore IL2026, IL3050
		}
	}
}
