﻿
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a .NET MAUI source generator.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: global::Microsoft.Maui.Controls.Xaml.XamlResourceId("PetSitterConnect.Resources.Styles.Colors.xaml", "Resources/Styles/Colors.xaml", typeof(global::__XamlGeneratedCode__.__Type678D5C1160E764FF))]
namespace __XamlGeneratedCode__
{
	[global::Microsoft.Maui.Controls.Xaml.XamlFilePath("Resources/Styles/Colors.xaml")]
	[global::Microsoft.Maui.Controls.Xaml.XamlCompilation(global::Microsoft.Maui.Controls.Xaml.XamlCompilationOptions.Compile)]
	[global::System.ComponentModel.EditorBrowsable(global::System.ComponentModel.EditorBrowsableState.Never)]
	public partial class __Type678D5C1160E764FF : global::Microsoft.Maui.Controls.ResourceDictionary
	{
		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
		public __Type678D5C1160E764FF()
		{
			InitializeComponent();
		}

		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
#if NET5_0_OR_GREATER
#endif
		private void InitializeComponent()
		{
#pragma warning disable IL2026, IL3050 // The body of InitializeComponent will be replaced by XamlC so LoadFromXaml will never be called in production builds
			global::Microsoft.Maui.Controls.Xaml.Extensions.LoadFromXaml(this, typeof(__Type678D5C1160E764FF));
#pragma warning restore IL2026, IL3050
		}
	}
}
