﻿
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a .NET MAUI source generator.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: global::Microsoft.Maui.Controls.Xaml.XamlResourceId("PetSitterConnect.AppShell.xaml", "AppShell.xaml", typeof(global::PetSitterConnect.AppShell))]
namespace PetSitterConnect
{
	[global::Microsoft.Maui.Controls.Xaml.XamlFilePath("AppShell.xaml")]
	public partial class AppShell : global::Microsoft.Maui.Controls.Shell
	{
		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
		private global::Microsoft.Maui.Controls.TabBar MainTabBar;

		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
		private global::Microsoft.Maui.Controls.ShellContent FindSittersTab;

		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
		private global::Microsoft.Maui.Controls.ShellContent AvailableRequestsTab;

		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
		private global::Microsoft.Maui.Controls.ShellContent BookingsTab;

		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
		private global::Microsoft.Maui.Controls.ShellContent MessagesTab;

		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
		private global::Microsoft.Maui.Controls.ShellContent CalendarTab;

		[global::System.CodeDom.Compiler.GeneratedCode("Microsoft.Maui.Controls.SourceGen", "1.0.0.0")]
#if NET5_0_OR_GREATER
		[global::System.Diagnostics.CodeAnalysis.MemberNotNullAttribute(nameof(MainTabBar))]
		[global::System.Diagnostics.CodeAnalysis.MemberNotNullAttribute(nameof(FindSittersTab))]
		[global::System.Diagnostics.CodeAnalysis.MemberNotNullAttribute(nameof(AvailableRequestsTab))]
		[global::System.Diagnostics.CodeAnalysis.MemberNotNullAttribute(nameof(BookingsTab))]
		[global::System.Diagnostics.CodeAnalysis.MemberNotNullAttribute(nameof(MessagesTab))]
		[global::System.Diagnostics.CodeAnalysis.MemberNotNullAttribute(nameof(CalendarTab))]
#endif
		private void InitializeComponent()
		{
#pragma warning disable IL2026, IL3050 // The body of InitializeComponent will be replaced by XamlC so LoadFromXaml will never be called in production builds
			global::Microsoft.Maui.Controls.Xaml.Extensions.LoadFromXaml(this, typeof(AppShell));
			MainTabBar = global::Microsoft.Maui.Controls.NameScopeExtensions.FindByName<global::Microsoft.Maui.Controls.TabBar>(this, "MainTabBar");
			FindSittersTab = global::Microsoft.Maui.Controls.NameScopeExtensions.FindByName<global::Microsoft.Maui.Controls.ShellContent>(this, "FindSittersTab");
			AvailableRequestsTab = global::Microsoft.Maui.Controls.NameScopeExtensions.FindByName<global::Microsoft.Maui.Controls.ShellContent>(this, "AvailableRequestsTab");
			BookingsTab = global::Microsoft.Maui.Controls.NameScopeExtensions.FindByName<global::Microsoft.Maui.Controls.ShellContent>(this, "BookingsTab");
			MessagesTab = global::Microsoft.Maui.Controls.NameScopeExtensions.FindByName<global::Microsoft.Maui.Controls.ShellContent>(this, "MessagesTab");
			CalendarTab = global::Microsoft.Maui.Controls.NameScopeExtensions.FindByName<global::Microsoft.Maui.Controls.ShellContent>(this, "CalendarTab");
#pragma warning restore IL2026, IL3050
		}
	}
}
