{"runtimeOptions": {"tfm": "net9.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "9.0.0"}, {"name": "Microsoft.Android", "version": ""}], "configProperties": {"MVVMTOOLKIT_ENABLE_INOTIFYPROPERTYCHANGING_SUPPORT": true, "Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability": true, "System.ComponentModel.DefaultValueAttribute.IsSupported": true, "System.Diagnostics.Metrics.Meter.IsSupported": false, "System.Diagnostics.Tracing.EventSource.IsSupported": false, "System.Globalization.Invariant": false, "System.Net.Http.EnableActivityPropagation": false, "System.Net.Http.UseNativeHttpHandler": true, "System.Reflection.NullabilityInfoContext.IsSupported": true, "System.Runtime.InteropServices.BuiltInComInterop.IsSupported": false, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "System.StartupHookProvider.IsSupported": false, "System.Text.Encoding.EnableUnsafeUTF7Encoding": false, "System.Text.Json.JsonSerializer.IsReflectionEnabledByDefault": true, "Xamarin.Android.Net.UseNegotiateAuthentication": false, "Switch.System.Reflection.ForceInterpretedInvoke": true, "Microsoft.Extensions.DependencyInjection.DisableDynamicEngine": true, "Microsoft.Maui.RuntimeFeature.IsIVisualAssemblyScanningEnabled": false, "Microsoft.Maui.RuntimeFeature.AreBindingInterceptorsSupported": true, "Microsoft.Maui.RuntimeFeature.IsXamlCBindingWithSourceCompilationEnabled": true}}}